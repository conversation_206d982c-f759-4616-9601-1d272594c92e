const Testimonials = () => {
  const testimonials = [
    {
      quote: "Connected my wardrobe to weather seamlessly. Finally, no more outfit regrets on rainy days.",
      author: "<PERSON>",
      role: "Fashion Enthusiast"
    },
    {
      quote: "Elegant, intuitive, and fashion-forward. This app understands style better than I do.",
      author: "<PERSON>",
      role: "Creative Director"
    },
    {
      quote: "The scheduling feature is a game-changer. I plan my whole week's outfits in minutes.",
      author: "<PERSON>",
      role: "Business Professional"
    }
  ];

  return (
    <section className="py-24 relative">
      <div className="container mx-auto px-6">
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-light text-white mb-6 text-shadow">
            What Our Users Say
          </h2>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-6xl mx-auto">
          {testimonials.map((testimonial, index) => (
            <div 
              key={index}
              className="feature-card glass-refraction group"
              style={{
                animationDelay: `${index * 0.3}s`
              }}
            >
              {/* Quote */}
              <div className="mb-6">
                <div className="text-4xl text-white/20 mb-4 font-serif">"</div>
                <p className="text-white/90 text-lg font-light leading-relaxed italic">
                  {testimonial.quote}
                </p>
              </div>

              {/* Author */}
              <div className="flex items-center space-x-4">
                <div className="w-12 h-12 rounded-full bg-white/10 flex items-center justify-center">
                  <span className="text-white font-medium">
                    {testimonial.author.split(' ').map(n => n[0]).join('')}
                  </span>
                </div>
                <div>
                  <div className="text-white font-medium">{testimonial.author}</div>
                  <div className="text-white/60 text-sm">{testimonial.role}</div>
                </div>
              </div>

              {/* Hover Glow Effect */}
              <div className="absolute inset-0 rounded-2xl opacity-0 group-hover:opacity-20 transition-opacity duration-500 bg-gradient-to-r from-white/10 to-transparent pointer-events-none" />
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default Testimonials;