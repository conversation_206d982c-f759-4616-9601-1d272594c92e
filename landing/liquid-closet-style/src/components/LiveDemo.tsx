import { useState } from "react";
import { Thermometer, Wind, Droplets, Sun } from "lucide-react";
import { Button } from "@/components/ui/button";

const LiveDemo = () => {
  const [activeTab, setActiveTab] = useState('weather');
  
  const weatherData = {
    temp: 72,
    condition: 'Partly Cloudy',
    humidity: 65,
    wind: 8
  };

  const outfitSuggestions = [
    { item: 'Light Blazer', color: 'Navy Blue', suitable: true },
    { item: 'Cotton Blouse', color: 'White', suitable: true },
    { item: 'Skinny Jeans', color: 'Dark Wash', suitable: true },
    { item: 'Wool Coat', color: 'Charcoal', suitable: false }
  ];

  return (
    <section className="py-24 relative">
      <div className="container mx-auto px-6">
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-light text-white mb-6 text-shadow">
            Try It Live
          </h2>
          <p className="text-xl text-white/80 max-w-2xl mx-auto font-light">
            Experience the magic of intelligent styling in real-time
          </p>
        </div>

        <div className="max-w-4xl mx-auto">
          <div className="glass-panel glass-refraction p-8 rounded-3xl">
            {/* Demo Navigation */}
            <div className="flex justify-center mb-8">
              <div className="glass-panel p-2 rounded-full">
                <div className="flex space-x-1">
                  {[
                    { id: 'weather', label: 'Weather' },
                    { id: 'closet', label: 'Closet' },
                    { id: 'schedule', label: 'Schedule' }
                  ].map((tab) => (
                    <Button
                      key={tab.id}
                      variant="ghost"
                      className={`px-6 py-2 rounded-full transition-all duration-300 ${
                        activeTab === tab.id 
                          ? 'bg-white/20 text-white' 
                          : 'text-white/70 hover:text-white hover:bg-white/10'
                      }`}
                      onClick={() => setActiveTab(tab.id)}
                    >
                      {tab.label}
                    </Button>
                  ))}
                </div>
              </div>
            </div>

            {/* Demo Content */}
            <div className="min-h-80">
              {activeTab === 'weather' && (
                <div className="animate-fade-in-scale">
                  {/* Weather Display */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                    <div className="space-y-6">
                      <h3 className="text-2xl font-medium text-white mb-4">Today's Weather</h3>
                      
                      <div className="glass-panel p-6 rounded-2xl">
                        <div className="flex items-center justify-between mb-4">
                          <div className="flex items-center space-x-3">
                            <Sun className="w-8 h-8 text-yellow-400" />
                            <div>
                              <div className="text-3xl font-light text-white">{weatherData.temp}°F</div>
                              <div className="text-white/70">{weatherData.condition}</div>
                            </div>
                          </div>
                        </div>
                        
                        <div className="grid grid-cols-2 gap-4 pt-4 border-t border-white/10">
                          <div className="flex items-center space-x-2">
                            <Droplets className="w-4 h-4 text-blue-400" />
                            <span className="text-white/80 text-sm">{weatherData.humidity}%</span>
                          </div>
                          <div className="flex items-center space-x-2">
                            <Wind className="w-4 h-4 text-gray-400" />
                            <span className="text-white/80 text-sm">{weatherData.wind} mph</span>
                          </div>
                        </div>
                      </div>
                    </div>

                    <div className="space-y-6">
                      <h3 className="text-2xl font-medium text-white mb-4">Recommended Items</h3>
                      
                      <div className="space-y-3">
                        {outfitSuggestions.map((item, index) => (
                          <div
                            key={index}
                            className={`glass-panel p-4 rounded-xl transition-all duration-300 ${
                              item.suitable 
                                ? 'border-green-400/30 bg-green-400/5' 
                                : 'border-red-400/30 bg-red-400/5 opacity-50'
                            }`}
                          >
                            <div className="flex items-center justify-between">
                              <div>
                                <div className="text-white font-medium">{item.item}</div>
                                <div className="text-white/60 text-sm">{item.color}</div>
                              </div>
                              <div className={`w-3 h-3 rounded-full ${
                                item.suitable ? 'bg-green-400' : 'bg-red-400'
                              }`} />
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {activeTab === 'closet' && (
                <div className="animate-fade-in-scale text-center py-16">
                  <div className="glass-panel p-8 rounded-2xl max-w-md mx-auto">
                    <Thermometer className="w-16 h-16 text-white/60 mx-auto mb-4" />
                    <h3 className="text-xl font-medium text-white mb-2">Smart Closet</h3>
                    <p className="text-white/70">
                      Browse and organize your wardrobe with intelligent categorization
                    </p>
                  </div>
                </div>
              )}

              {activeTab === 'schedule' && (
                <div className="animate-fade-in-scale text-center py-16">
                  <div className="glass-panel p-8 rounded-2xl max-w-md mx-auto">
                    <Thermometer className="w-16 h-16 text-white/60 mx-auto mb-4" />
                    <h3 className="text-xl font-medium text-white mb-2">Schedule Planner</h3>
                    <p className="text-white/70">
                      Plan outfits for upcoming events with smart suggestions
                    </p>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default LiveDemo;