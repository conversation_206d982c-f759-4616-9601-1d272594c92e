import { useState } from "react";
import { ChevronLeft, ChevronRight } from "lucide-react";
import { Button } from "@/components/ui/button";

const MockupShowcase = () => {
  const [currentSlide, setCurrentSlide] = useState(0);
  
  const mockups = [
    {
      title: "Home Dashboard",
      description: "Weather-aware outfit recommendations at a glance",
      image: "/placeholder.svg"
    },
    {
      title: "Smart Closet",
      description: "Organize and browse your wardrobe effortlessly",
      image: "/placeholder.svg"
    },
    {
      title: "Schedule Planner",
      description: "Plan your outfits for upcoming events and occasions",
      image: "/placeholder.svg"
    }
  ];

  const nextSlide = () => {
    setCurrentSlide((prev) => (prev + 1) % mockups.length);
  };

  const prevSlide = () => {
    setCurrentSlide((prev) => (prev - 1 + mockups.length) % mockups.length);
  };

  return (
    <section className="py-24 relative">
      <div className="container mx-auto px-6">
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-light text-white mb-6 text-shadow">
            Experience ClosetMate
          </h2>
          <p className="text-xl text-white/80 max-w-2xl mx-auto font-light">
            Discover how our intuitive interface transforms your daily styling routine
          </p>
        </div>

        {/* Carousel Container */}
        <div className="relative max-w-4xl mx-auto">
          {/* Mockup Display */}
          <div className="glass-panel glass-refraction p-8 rounded-3xl overflow-hidden">
            <div className="relative h-96 md:h-[500px] rounded-2xl overflow-hidden">
              {mockups.map((mockup, index) => (
                <div
                  key={index}
                  className={`absolute inset-0 transition-all duration-700 ${
                    index === currentSlide 
                      ? 'opacity-100 transform translate-x-0' 
                      : index < currentSlide 
                        ? 'opacity-0 transform -translate-x-full' 
                        : 'opacity-0 transform translate-x-full'
                  }`}
                >
                  {/* Mockup Frame */}
                  <div className="h-full bg-gradient-to-br from-muted to-secondary rounded-2xl p-8 flex items-center justify-center">
                    <div className="text-center">
                      <div className="w-32 h-32 mx-auto mb-6 bg-white/20 rounded-full flex items-center justify-center">
                        <span className="text-4xl font-light text-white">
                          {mockup.title.charAt(0)}
                        </span>
                      </div>
                      <h3 className="text-2xl font-medium text-white mb-3">
                        {mockup.title}
                      </h3>
                      <p className="text-white/70">
                        {mockup.description}
                      </p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Navigation */}
          <div className="flex justify-center items-center mt-8 space-x-4">
            <Button
              variant="outline"
              size="icon"
              className="glass-panel border-white/20 text-white hover:bg-white/10"
              onClick={prevSlide}
            >
              <ChevronLeft className="h-4 w-4" />
            </Button>

            {/* Dots Indicator */}
            <div className="flex space-x-2">
              {mockups.map((_, index) => (
                <button
                  key={index}
                  className={`w-3 h-3 rounded-full transition-all duration-300 ${
                    index === currentSlide 
                      ? 'bg-white' 
                      : 'bg-white/30 hover:bg-white/50'
                  }`}
                  onClick={() => setCurrentSlide(index)}
                />
              ))}
            </div>

            <Button
              variant="outline"
              size="icon"
              className="glass-panel border-white/20 text-white hover:bg-white/10"
              onClick={nextSlide}
            >
              <ChevronRight className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>
    </section>
  );
};

export default MockupShowcase;