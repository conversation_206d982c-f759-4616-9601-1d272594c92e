import { Button } from "@/components/ui/button";

const FinalCTA = () => {
  return (
    <section className="py-24 relative">
      <div className="container mx-auto px-6">
        <div className="max-w-3xl mx-auto text-center">
          {/* Main CTA */}
          <div className="glass-panel glass-refraction p-12 rounded-3xl animate-fade-in-scale">
            <h2 className="text-4xl md:text-5xl font-light text-white mb-8 text-shadow leading-tight">
              Use ClosetMate now and elevate your daily style
            </h2>
            
            <p className="text-xl text-white/80 mb-10 font-light max-w-2xl mx-auto leading-relaxed">
              Join thousands who've transformed their wardrobe experience with intelligent styling and weather-aware recommendations.
            </p>
            
            <Button 
              className="glass-button shimmer text-xl px-16 py-8 animate-float"
              onClick={() => {
                // In a real app, this would navigate to sign-up or app
                console.log('Start Styling clicked');
              }}
            >
              Start Styling
            </Button>
          </div>
        </div>
      </div>
    </section>
  );
};

export default FinalCTA;