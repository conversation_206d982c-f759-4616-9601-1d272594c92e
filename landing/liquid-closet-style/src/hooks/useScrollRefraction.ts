import { useEffect } from 'react';

export const useScrollRefraction = () => {
  useEffect(() => {
    const handleScroll = () => {
      const scrollY = window.scrollY;
      const windowHeight = window.innerHeight;
      
      // Get all glass elements
      const glassElements = document.querySelectorAll('.glass-refraction');
      
      glassElements.forEach((element) => {
        const rect = element.getBoundingClientRect();
        const elementTop = rect.top + scrollY;
        const elementHeight = rect.height;
        
        // Calculate if element is in viewport and scroll progress
        const elementCenter = elementTop + elementHeight / 2;
        const distanceFromCenter = Math.abs(elementCenter - (scrollY + windowHeight / 2));
        const maxDistance = windowHeight;
        
        // Normalize distance (0 = center of screen, 1 = edge of screen)
        const normalizedDistance = Math.min(distanceFromCenter / maxDistance, 1);
        
        // Calculate refraction intensity based on scroll position
        const scrollProgress = scrollY / (document.documentElement.scrollHeight - windowHeight);
        const refractionIntensity = Math.sin(scrollProgress * Math.PI * 4) * 0.5 + 0.5;
        
        // Apply refraction transforms
        const refractionAngle = (1 - normalizedDistance) * refractionIntensity * 3;
        const refractionScale = 1 + (1 - normalizedDistance) * refractionIntensity * 0.02;
        const refractionSkew = (1 - normalizedDistance) * refractionIntensity * 1;
        
        // Apply the transforms
        (element as HTMLElement).style.transform = `
          perspective(1000px) 
          rotateX(${refractionAngle * 0.5}deg) 
          rotateY(${refractionAngle}deg) 
          scale(${refractionScale})
          skewX(${refractionSkew * 0.5}deg)
        `;
        
        // Add/remove scroll-active class for CSS animations
        if (normalizedDistance < 0.8 && refractionIntensity > 0.3) {
          element.classList.add('scroll-active');
        } else {
          element.classList.remove('scroll-active');
        }
        
        // Apply subtle filter effects for light refraction
        const hueRotation = refractionIntensity * 5;
        const brightness = 1 + refractionIntensity * 0.1;
        (element as HTMLElement).style.filter = `
          hue-rotate(${hueRotation}deg) 
          brightness(${brightness})
          contrast(${1 + refractionIntensity * 0.05})
        `;
      });
      
      // Apply parallax refraction to background
      const backgroundElements = document.querySelectorAll('.parallax');
      backgroundElements.forEach((element) => {
        const speed = 0.3;
        const refractionOffset = Math.sin(scrollY * 0.001) * 2;
        (element as HTMLElement).style.transform = `
          translateY(${scrollY * speed}px) 
          translateX(${refractionOffset}px)
          scale(${1.1 + Math.sin(scrollY * 0.002) * 0.01})
        `;
      });
    };

    // Throttle scroll events for performance
    let ticking = false;
    const throttledScroll = () => {
      if (!ticking) {
        requestAnimationFrame(() => {
          handleScroll();
          ticking = false;
        });
        ticking = true;
      }
    };

    window.addEventListener('scroll', throttledScroll, { passive: true });
    
    // Initial call
    handleScroll();
    
    return () => {
      window.removeEventListener('scroll', throttledScroll);
    };
  }, []);
};

export default useScrollRefraction;