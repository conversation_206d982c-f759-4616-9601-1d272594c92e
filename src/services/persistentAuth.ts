import { auth<PERSON>pi, user<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, User, AuthCredentials } from './backendApi';
import { UserProfile, UserProfileFormData } from '@/types/user';

// Local storage keys
const CURRENT_USER_KEY = 'current-user';
const CURRENT_USER_PROFILE_KEY = 'current-user-profile';

export interface AuthResult {
  success: boolean;
  user?: User;
  error?: string;
}

export interface PasswordValidationResult {
  isValid: boolean;
  errors: string[];
}

/**
 * Persistent Authentication Service
 * Uses PostgreSQL backend instead of Dexie for data persistence
 */
export class PersistentAuthService {
  /**
   * Hash a password using a simple but secure method
   * In a production app, you'd want to use a more robust library like bcrypt
   */
  private static async hashPassword(password: string): Promise<string> {
    try {
      // Check if crypto.subtle is available
      if (typeof crypto !== 'undefined' && crypto.subtle) {
        const encoder = new TextEncoder();
        const data = encoder.encode(password + 'closet-glass-chic-salt');
        const hashBuffer = await crypto.subtle.digest('SHA-256', data);
        const hashArray = Array.from(new Uint8Array(hashBuffer));
        return hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
      } else {
        // Fallback to a simple hash for development/testing
        return this.simpleHash(password + 'closet-glass-chic-salt');
      }
    } catch (error) {
      console.error('Password hashing failed:', error);
      return this.simpleHash(password + 'closet-glass-chic-salt');
    }
  }

  /**
   * Simple hash function fallback
   */
  private static simpleHash(str: string): string {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32bit integer
    }
    return Math.abs(hash).toString(16);
  }

  /**
   * Validate email format
   */
  static validateEmail(email: string): boolean {
    const emailRegex = /^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$/;
    return emailRegex.test(email);
  }

  /**
   * Validate password strength
   */
  static validatePassword(password: string): PasswordValidationResult {
    const errors: string[] = [];

    if (password.length < 8) {
      errors.push('Password must be at least 8 characters long');
    }

    if (!/[A-Z]/.test(password)) {
      errors.push('Password must contain at least one uppercase letter');
    }

    if (!/[a-z]/.test(password)) {
      errors.push('Password must contain at least one lowercase letter');
    }

    if (!/\d/.test(password)) {
      errors.push('Password must contain at least one number');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Register a new user and create their profile
   */
  static async register(credentials: AuthCredentials, profileData: Omit<UserProfile, 'id' | 'email' | 'createdAt' | 'updatedAt'>): Promise<AuthResult> {
    try {
      console.log('🔐 Starting user registration process...');

      // Validate email
      if (!this.validateEmail(credentials.email)) {
        console.log('❌ Email validation failed');
        return {
          success: false,
          error: 'Invalid email format'
        };
      }

      // Validate password
      const passwordValidation = this.validatePassword(credentials.password);
      if (!passwordValidation.isValid) {
        console.log('❌ Password validation failed:', passwordValidation.errors);
        return {
          success: false,
          error: passwordValidation.errors.join(', ')
        };
      }

      // Register user with backend
      console.log('📡 Registering user with backend...');
      const user = await authApi.register(credentials);

      // Create user profile
      console.log('👤 Creating user profile...');
      const userProfile = await userProfileApi.create({
        ...profileData,
        userId: user.id
      });

      // Store user and profile in localStorage
      this.setCurrentUser(user);
      this.setCurrentUserProfile(userProfile);

      console.log('✅ Registration completed successfully');
      return {
        success: true,
        user
      };

    } catch (error) {
      console.error('❌ Registration error:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      return {
        success: false,
        error: `Registration failed: ${errorMessage}`
      };
    }
  }

  /**
   * Sign in an existing user
   */
  static async signIn(credentials: AuthCredentials): Promise<AuthResult> {
    try {
      console.log('🔐 Starting user sign in process...');

      // Validate email format
      if (!this.validateEmail(credentials.email)) {
        console.log('❌ Email validation failed');
        return {
          success: false,
          error: 'Invalid email format'
        };
      }

      // Sign in with backend
      console.log('📡 Signing in with backend...');
      const user = await authApi.login(credentials);

      // Get user profile
      console.log('👤 Fetching user profile...');
      const userProfile = await userProfileApi.getByUserId(user.id);

      // Store user and profile in localStorage
      this.setCurrentUser(user);
      if (userProfile) {
        this.setCurrentUserProfile(userProfile);
      }

      console.log('✅ Sign in successful');
      return {
        success: true,
        user
      };

    } catch (error) {
      console.error('❌ Sign in error:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      return {
        success: false,
        error: `Sign in failed: ${errorMessage}`
      };
    }
  }

  /**
   * Get current authenticated user
   */
  static async getCurrentUser(): Promise<User | null> {
    try {
      const userJson = localStorage.getItem(CURRENT_USER_KEY);
      if (!userJson) {
        return null;
      }

      const user = JSON.parse(userJson) as User;
      
      // Verify user still exists in backend
      const backendUser = await authApi.getUserById(user.id);
      if (!backendUser) {
        // User no longer exists, clear local storage
        this.signOut();
        return null;
      }

      return backendUser;
    } catch (error) {
      console.error('❌ Get current user error:', error);
      return null;
    }
  }

  /**
   * Get current user profile
   */
  static getCurrentUserProfile(): UserProfile | null {
    try {
      const profileJson = localStorage.getItem(CURRENT_USER_PROFILE_KEY);
      if (!profileJson) {
        return null;
      }

      return JSON.parse(profileJson) as UserProfile;
    } catch (error) {
      console.error('❌ Get current user profile error:', error);
      return null;
    }
  }

  /**
   * Refresh user profile from backend
   */
  static async refreshUserProfile(): Promise<UserProfile | null> {
    try {
      const currentUser = await this.getCurrentUser();
      if (!currentUser) {
        return null;
      }

      const userProfile = await userProfileApi.getByUserId(currentUser.id);
      if (userProfile) {
        this.setCurrentUserProfile(userProfile);
      }

      return userProfile;
    } catch (error) {
      console.error('❌ Refresh user profile error:', error);
      return null;
    }
  }

  /**
   * Set current user session
   */
  static setCurrentUser(user: User): void {
    localStorage.setItem(CURRENT_USER_KEY, JSON.stringify(user));
  }

  /**
   * Set current user profile
   */
  static setCurrentUserProfile(profile: UserProfile): void {
    localStorage.setItem(CURRENT_USER_PROFILE_KEY, JSON.stringify(profile));
  }

  /**
   * Sign out current user
   */
  static signOut(): void {
    localStorage.removeItem(CURRENT_USER_KEY);
    localStorage.removeItem(CURRENT_USER_PROFILE_KEY);
  }

  /**
   * Check if user has completed profile setup
   */
  static hasCompletedProfile(): boolean {
    const profile = this.getCurrentUserProfile();
    return profile !== null &&
           profile.firstName &&
           profile.lastName &&
           profile.dateOfBirth &&
           profile.cityName;
  }
}

export default PersistentAuthService;
