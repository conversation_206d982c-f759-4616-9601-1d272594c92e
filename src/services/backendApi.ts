// Backend API service for communicating with the PostgreSQL-backed API
import { UserProfile, UserProfileFormData } from '@/types/user';
import { apiLogger } from '@/utils/logger';
import { apiConfig } from '@/config/environment';

// API configuration
const API_BASE_URL = apiConfig.baseUrl;

// API error class
export class BackendApiError extends Error {
  constructor(
    message: string,
    public status?: number,
    public code?: string,
    public details?: any
  ) {
    super(message);
    this.name = 'BackendApiError';
  }
}

// Generic API request function
async function apiRequest<T>(
  endpoint: string,
  options: RequestInit = {}
): Promise<T> {
  const url = `${API_BASE_URL}${endpoint}`;
  
  apiLogger.debug('Making API request', {
    method: options.method || 'GET',
    url,
    hasBody: !!options.body
  });

  try {
    const response = await fetch(url, {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
      ...options,
    });

    const data = await response.json();

    if (!response.ok) {
      apiLogger.error('API request failed', {
        status: response.status,
        statusText: response.statusText,
        url,
        error: data.error || 'Unknown error'
      });

      throw new BackendApiError(
        data.error || `HTTP ${response.status}: ${response.statusText}`,
        response.status,
        data.code,
        data.details
      );
    }

    apiLogger.debug('API request successful', {
      url,
      status: response.status
    });

    return data;
  } catch (error) {
    if (error instanceof BackendApiError) {
      throw error;
    }

    apiLogger.error('API request failed with network error', {
      url,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, error instanceof Error ? error : undefined);

    throw new BackendApiError(
      `Network error: ${error instanceof Error ? error.message : 'Unknown error'}`,
      0,
      'NETWORK_ERROR'
    );
  }
}

// Authentication interfaces
export interface User {
  id: string;
  email: string;
  createdAt: string;
  lastLoginAt?: string;
}

export interface AuthCredentials {
  email: string;
  password: string;
}

// Authentication API functions
export const authApi = {
  // Register a new user
  async register(credentials: AuthCredentials): Promise<User> {
    apiLogger.info('Registering new user via backend API', {
      email: credentials.email
    });

    const response = await apiRequest<{ success: boolean; user: User }>('/auth/register', {
      method: 'POST',
      body: JSON.stringify(credentials),
    });

    apiLogger.info('User registered successfully', {
      userId: response.user.id,
      email: response.user.email
    });

    return response.user;
  },

  // Sign in user
  async login(credentials: AuthCredentials): Promise<User> {
    apiLogger.info('Signing in user via backend API', {
      email: credentials.email
    });

    const response = await apiRequest<{ success: boolean; user: User }>('/auth/login', {
      method: 'POST',
      body: JSON.stringify(credentials),
    });

    apiLogger.info('User signed in successfully', {
      userId: response.user.id,
      email: response.user.email
    });

    return response.user;
  },

  // Get user by ID
  async getUserById(id: string): Promise<User | null> {
    apiLogger.debug('Fetching user by ID', { id });

    try {
      const response = await apiRequest<{ success: boolean; user: User }>(`/auth/user/${id}`);
      return response.user;
    } catch (error) {
      if (error instanceof BackendApiError && error.status === 404) {
        apiLogger.warn('User not found', { id });
        return null;
      }
      throw error;
    }
  },

  // Validate email availability
  async validateEmail(email: string): Promise<{ available: boolean; message: string }> {
    apiLogger.debug('Validating email availability', { email });

    const response = await apiRequest<{ success: boolean; available: boolean; message: string }>('/auth/validate-email', {
      method: 'POST',
      body: JSON.stringify({ email }),
    });

    return {
      available: response.available,
      message: response.message
    };
  }
};

// User Profile API functions
export const userProfileApi = {
  // Create a new user profile
  async create(profileData: Omit<UserProfile, 'id' | 'createdAt' | 'updatedAt'> & { userId: string }): Promise<UserProfile> {
    apiLogger.info('Creating user profile via backend API', {
      firstName: profileData.firstName,
      lastName: profileData.lastName,
      cityName: profileData.cityName
    });

    const response = await apiRequest<{ success: boolean; data: UserProfile }>('/user-profile', {
      method: 'POST',
      body: JSON.stringify(profileData),
    });

    apiLogger.info('User profile created successfully', {
      id: response.data.id
    });

    return response.data;
  },

  // Get user profile by ID
  async getById(id: string): Promise<UserProfile | null> {
    apiLogger.debug('Fetching user profile by ID', { id });

    try {
      const response = await apiRequest<{ success: boolean; data: UserProfile }>(`/user-profile/${id}`);
      return response.data;
    } catch (error) {
      if (error instanceof BackendApiError && error.status === 404) {
        apiLogger.warn('User profile not found', { id });
        return null;
      }
      throw error;
    }
  },

  // Get user profile by user ID
  async getByUserId(userId: string): Promise<UserProfile | null> {
    apiLogger.debug('Fetching user profile by user ID', { userId });

    try {
      const response = await apiRequest<{ success: boolean; data: UserProfile }>(`/user-profile/user/${userId}`);
      return response.data;
    } catch (error) {
      if (error instanceof BackendApiError && error.status === 404) {
        apiLogger.warn('User profile not found for user', { userId });
        return null;
      }
      throw error;
    }
  },

  // Update user profile
  async update(id: string, updateData: Partial<Omit<UserProfile, 'id' | 'createdAt' | 'updatedAt'>>): Promise<UserProfile | null> {
    apiLogger.info('Updating user profile', { id });

    try {
      const response = await apiRequest<{ success: boolean; data: UserProfile }>(`/user-profile/${id}`, {
        method: 'PUT',
        body: JSON.stringify(updateData),
      });

      return response.data;
    } catch (error) {
      if (error instanceof BackendApiError && error.status === 404) {
        apiLogger.warn('User profile not found for update', { id });
        return null;
      }
      throw error;
    }
  },

  // Delete user profile
  async delete(id: string): Promise<boolean> {
    apiLogger.info('Deleting user profile', { id });

    try {
      await apiRequest<{ success: boolean }>(`/user-profile/${id}`, {
        method: 'DELETE',
      });

      return true;
    } catch (error) {
      if (error instanceof BackendApiError && error.status === 404) {
        apiLogger.warn('User profile not found for deletion', { id });
        return false;
      }
      throw error;
    }
  },

  // Get all user profiles (with pagination)
  async getAll(limit: number = 50, offset: number = 0): Promise<{ profiles: UserProfile[]; total: number }> {
    apiLogger.debug('Fetching all user profiles', { limit, offset });

    const response = await apiRequest<{ 
      success: boolean; 
      data: { profiles: UserProfile[]; total: number } 
    }>(`/user-profile?limit=${limit}&offset=${offset}`);

    return response.data;
  }
};

// Health check function with timeout
export async function checkBackendHealth(): Promise<{ healthy: boolean; details?: any }> {
  try {
    apiLogger.debug('Checking backend health');

    // Create a timeout promise
    const timeoutPromise = new Promise((_, reject) => {
      setTimeout(() => reject(new Error('Health check timeout')), 5000); // 5 second timeout
    });

    // Race between fetch and timeout
    const response = await Promise.race([
      fetch(`${API_BASE_URL.replace('/api', '')}/health`),
      timeoutPromise
    ]) as Response;

    const data = await response.json();

    if (response.ok) {
      apiLogger.debug('Backend health check passed');
      return { healthy: true, details: data };
    } else {
      apiLogger.warn('Backend health check failed', { status: response.status });
      return { healthy: false, details: data };
    }
  } catch (error) {
    apiLogger.error('Backend health check failed with error', {
      error: error instanceof Error ? error.message : 'Unknown error'
    }, error instanceof Error ? error : undefined);

    return {
      healthy: false,
      details: {
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    };
  }
}

// Convert UserProfileFormData to backend format
export function convertFormDataToProfile(formData: UserProfileFormData): Omit<UserProfile, 'id' | 'createdAt' | 'updatedAt'> {
  if (!formData.selectedCity) {
    throw new Error('Selected city is required');
  }

  return {
    firstName: formData.firstName,
    lastName: formData.lastName,
    gender: formData.gender as 'Male' | 'Female' | 'Other' | 'Prefer not to say',
    dateOfBirth: formData.dateOfBirth,
    cityName: formData.cityName,
    latitude: parseFloat(formData.selectedCity.lat),
    longitude: parseFloat(formData.selectedCity.lon),
    weatherPreferences: {}
  };
}

// Test backend connectivity
export async function testBackendConnectivity(): Promise<boolean> {
  try {
    const health = await checkBackendHealth();
    return health.healthy;
  } catch (error) {
    apiLogger.error('Backend connectivity test failed', {
      error: error instanceof Error ? error.message : 'Unknown error'
    }, error instanceof Error ? error : undefined);
    return false;
  }
}

// Get API configuration info
export function getApiConfig() {
  return {
    baseUrl: API_BASE_URL,
    endpoints: {
      userProfile: '/user-profile',
      weather: '/weather',
      cities: '/cities',
      upload: '/upload'
    }
  };
}
