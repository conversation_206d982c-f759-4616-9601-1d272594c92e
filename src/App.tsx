import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import { Layout } from "./components/Layout";
import { Home } from "./pages/Home";
import { Closet } from "./pages/Closet";
import { Schedule } from "./pages/Schedule";
import { Profile } from "./pages/Profile";
import { SimpleLandingPage } from "./pages/SimpleLandingPage";
import { LoginPage } from "./pages/LoginPage";
import NotFound from "./pages/NotFound";
import { DebugOnboarding } from "./components/DebugOnboarding";
import { OnboardingProvider } from "./contexts/OnboardingContext";
import { OnboardingRouter } from "./components/onboarding/OnboardingRouter";
import { useOnboarding } from "./hooks/useOnboarding";
import { checkBackendHealth } from "./services/backendApi";
import { ErrorBoundary } from "./components/ErrorBoundary";
import PersistentAuthService from "./services/persistentAuth";
import "./utils/debugConfig"; // Load debug utilities
import { AuthGuard } from "./components/AuthGuard";
import { useEffect, useState } from "react";
import { UserProfileFormData } from "./types/user";

const queryClient = new QueryClient();

// Authentication states
type AuthState = 'landing' | 'login' | 'onboarding' | 'authenticated';

// Main App Component with Authentication and Onboarding Integration
const MainApp = () => {
  const { isCompleted, resetOnboarding } = useOnboarding();
  const [authState, setAuthState] = useState<AuthState>('landing');
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [isCheckingAuth, setIsCheckingAuth] = useState(true);

  // Check backend health on app start
  useEffect(() => {
    checkBackendHealth().then(health => {
      if (!health.healthy) {
        console.warn('Backend health check failed:', health.details);
      } else {
        console.log('✅ Backend is healthy');
      }
    }).catch(error => {
      console.error('Failed to check backend health:', error);
    });
  }, []);

  // Check if user is already authenticated
  useEffect(() => {
    const checkAuthentication = async () => {
      try {
        console.log('🔍 Checking authentication...', { isCompleted });
        const currentUser = await PersistentAuthService.getCurrentUser();
        console.log('👤 Current user:', currentUser);

        if (currentUser) {
          // Check if user has a complete profile by checking the user profile data
          const userProfile = PersistentAuthService.getCurrentUserProfile();
          const hasCompleteProfile = userProfile &&
                                   userProfile.firstName &&
                                   userProfile.lastName &&
                                   userProfile.dateOfBirth &&
                                   userProfile.cityName;

          if (hasCompleteProfile || isCompleted) {
            console.log('✅ User authenticated and onboarding completed');
            setIsAuthenticated(true);
            setAuthState('authenticated');
          } else {
            console.log('🚀 User authenticated but onboarding not completed');
            setAuthState('onboarding');
          }
        } else {
          console.log('🏠 No user authenticated, showing landing page');
          setAuthState('landing');
        }
      } catch (error) {
        console.error('Authentication check failed:', error);
        setAuthState('landing');
      } finally {
        setIsCheckingAuth(false);
      }
    };

    checkAuthentication();
  }, [isCompleted]);

  const handleSignUp = () => {
    // Directly go to onboarding for signup
    setAuthState('onboarding');
  };

  const handleSignIn = () => {
    setAuthState('login');
  };

  const handleBackToLanding = () => {
    setAuthState('landing');
  };

  const handleLoginSuccess = async () => {
    try {
      const currentUser = await PersistentAuthService.getCurrentUser();
      if (currentUser) {
        // Check if user has a complete profile by checking the user profile data
        const userProfile = PersistentAuthService.getCurrentUserProfile();
        const hasCompleteProfile = userProfile &&
                                 userProfile.firstName &&
                                 userProfile.lastName &&
                                 userProfile.dateOfBirth &&
                                 userProfile.cityName;

        if (hasCompleteProfile || isCompleted) {
          setIsAuthenticated(true);
          setAuthState('authenticated');
        } else {
          setAuthState('onboarding');
        }
      } else {
        setAuthState('landing');
      }
    } catch (error) {
      console.error('Login success check failed:', error);
      setAuthState('landing');
    }
  };

  const handleOnboardingComplete = () => {
    setIsAuthenticated(true);
    setAuthState('authenticated');
    console.log('Onboarding completed successfully');
  };

  const handleResetOnboarding = () => {
    resetOnboarding();
    PersistentAuthService.signOut();
    setIsAuthenticated(false);
    setAuthState('landing');
  };

  const handleClearStorage = () => {
    localStorage.clear();
    window.location.reload();
  };

  // Show landing page for unauthenticated users
  if (authState === 'landing') {
    return (
      <SimpleLandingPage
        onSignUp={handleSignUp}
        onSignIn={handleSignIn}
      />
    );
  }

  // Show login page
  if (authState === 'login') {
    return (
      <LoginPage
        onBack={handleBackToLanding}
        onSignUp={handleSignUp}
        onSuccess={handleLoginSuccess}
      />
    );
  }

  // Show loading while checking authentication
  if (isCheckingAuth) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto mb-4"></div>
          <p>Loading...</p>
        </div>
      </div>
    );
  }

  // Show onboarding if user is authenticated but hasn't completed onboarding
  if (authState === 'onboarding') {
    return <OnboardingRouter onComplete={handleOnboardingComplete} />;
  }

  // Show main application for authenticated users who completed onboarding
  return (
    <AuthGuard fallback={
      <SimpleLandingPage
        onSignUp={handleSignUp}
        onSignIn={handleSignIn}
      />
    }>
      <BrowserRouter>
        <Layout>
          <Routes>
            <Route path="/" element={<Home />} />
            <Route path="/closet" element={<Closet />} />
            <Route path="/schedule" element={<Schedule />} />
            <Route path="/profile" element={<Profile />} />
            {/* ADD ALL CUSTOM ROUTES ABOVE THE CATCH-ALL "*" ROUTE */}
            <Route path="*" element={<NotFound />} />
          </Routes>

          {/* Development helper - remove in production */}
          {process.env.NODE_ENV === 'development' && (
            <div className="fixed bottom-4 left-4 z-50 flex flex-col gap-2">
              <button
                onClick={handleResetOnboarding}
                className="px-3 py-1 text-xs bg-red-500 text-white rounded opacity-50 hover:opacity-100"
                title="Reset Authentication & Onboarding (Dev Only)"
              >
                Reset Auth & Onboarding
              </button>
              <button
                onClick={handleClearStorage}
                className="px-3 py-1 text-xs bg-orange-500 text-white rounded opacity-50 hover:opacity-100"
                title="Clear All Storage (Dev Only)"
              >
                Clear Storage
              </button>
            </div>
          )}
        </Layout>
      </BrowserRouter>
    </AuthGuard>
  );
};

const App = () => (
  <ErrorBoundary>
    <QueryClientProvider client={queryClient}>
      <TooltipProvider>
        <OnboardingProvider>
          <Toaster />
          <Sonner />
          <MainApp />
        </OnboardingProvider>
      </TooltipProvider>
    </QueryClientProvider>
  </ErrorBoundary>
);

export default App;
