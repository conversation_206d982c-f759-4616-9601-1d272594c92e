import { useState } from 'react';
import { ArrowLeft, Eye, EyeOff, Mail, Lock, User, Sparkles, AlertCircle, Check } from 'lucide-react';
import { GlassCard } from '@/components/GlassCard';
import { GlassButton } from '@/components/ui/glass-button';
import { GlassInput } from '@/components/ui/glass-input';
import { cn } from '@/lib/utils';

interface SignUpPageProps {
  onBack: () => void;
  onSignIn: () => void;
  onSuccess: (formData: SignUpFormData) => void;
}

export interface SignUpFormData {
  firstName: string;
  lastName: string;
  email: string;
  password: string;
  confirmPassword: string;
}

interface SignUpErrors {
  firstName?: string;
  lastName?: string;
  email?: string;
  password?: string;
  confirmPassword?: string;
  general?: string;
}

export const SignUpPage = ({ onBack, onSignIn, onSuccess }: SignUpPageProps) => {
  const [formData, setFormData] = useState<SignUpFormData>({
    firstName: '',
    lastName: '',
    email: '',
    password: '',
    confirmPassword: ''
  });
  const [errors, setErrors] = useState<SignUpErrors>({});
  const [isLoading, setIsLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  const validatePassword = (password: string) => {
    const errors = [];
    if (password.length < 8) errors.push('At least 8 characters');
    if (!/[A-Z]/.test(password)) errors.push('One uppercase letter');
    if (!/[a-z]/.test(password)) errors.push('One lowercase letter');
    if (!/\d/.test(password)) errors.push('One number');
    return errors;
  };

  const validateForm = (): boolean => {
    const newErrors: SignUpErrors = {};

    // First name validation
    if (!formData.firstName.trim()) {
      newErrors.firstName = 'First name is required';
    }

    // Last name validation
    if (!formData.lastName.trim()) {
      newErrors.lastName = 'Last name is required';
    }

    // Email validation
    if (!formData.email) {
      newErrors.email = 'Email is required';
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = 'Please enter a valid email address';
    }

    // Password validation
    if (!formData.password) {
      newErrors.password = 'Password is required';
    } else {
      const passwordErrors = validatePassword(formData.password);
      if (passwordErrors.length > 0) {
        newErrors.password = `Password must have: ${passwordErrors.join(', ')}`;
      }
    }

    // Confirm password validation
    if (!formData.confirmPassword) {
      newErrors.confirmPassword = 'Please confirm your password';
    } else if (formData.password !== formData.confirmPassword) {
      newErrors.confirmPassword = 'Passwords do not match';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setIsLoading(true);
    setErrors({});

    try {
      // Pass the form data to the parent component to handle onboarding
      // The actual registration will happen in the onboarding flow
      setTimeout(() => {
        onSuccess(formData);
      }, 1000);
    } catch (error) {
      setErrors({
        general: 'An unexpected error occurred. Please try again.'
      });
      setIsLoading(false);
    }
  };

  const handleInputChange = (field: keyof SignUpFormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Clear field-specific error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: undefined }));
    }
  };

  const passwordRequirements = [
    { text: 'At least 8 characters', met: formData.password.length >= 8 },
    { text: 'One uppercase letter', met: /[A-Z]/.test(formData.password) },
    { text: 'One lowercase letter', met: /[a-z]/.test(formData.password) },
    { text: 'One number', met: /\d/.test(formData.password) }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-zara-white via-zara-light-gray/30 to-zara-white flex items-center justify-center p-6">
      <div className="w-full max-w-md">
        {/* Header */}
        <div className="text-center mb-8">
          <button
            onClick={onBack}
            className="inline-flex items-center text-zara-dark-gray hover:text-zara-charcoal transition-colors mb-6"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Home
          </button>
          
          <div className="flex items-center justify-center space-x-3 mb-6">
            <div className="w-12 h-12 rounded-xl bg-gradient-to-br from-zara-charcoal to-zara-dark-gray flex items-center justify-center">
              <Sparkles className="w-7 h-7 text-white" />
            </div>
            <span className="zara-h3 text-zara-charcoal">Closet Glass Chic</span>
          </div>
          
          <h1 className="zara-h2 text-zara-charcoal mb-2">Create Your Account</h1>
          <p className="zara-body text-zara-dark-gray">
            Join thousands of fashion enthusiasts
          </p>
        </div>

        {/* Sign Up Form */}
        <GlassCard variant="prominent" className="p-8">
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* General Error */}
            {errors.general && (
              <div className="glass-subtle p-4 rounded-xl border border-red-200">
                <div className="flex items-start space-x-3">
                  <AlertCircle className="w-5 h-5 text-red-500 mt-0.5 flex-shrink-0" />
                  <p className="zara-caption text-red-700">{errors.general}</p>
                </div>
              </div>
            )}

            {/* Name Fields */}
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <label className="zara-body text-zara-charcoal block">
                  First Name
                </label>
                <div className="relative">
                  <User className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-zara-dark-gray" />
                  <GlassInput
                    type="text"
                    placeholder="First name"
                    value={formData.firstName}
                    onChange={(e) => handleInputChange('firstName', e.target.value)}
                    className={cn(
                      "pl-11",
                      errors.firstName && "border-red-300 focus:border-red-500"
                    )}
                    disabled={isLoading}
                  />
                </div>
                {errors.firstName && (
                  <p className="zara-caption text-red-600">{errors.firstName}</p>
                )}
              </div>

              <div className="space-y-2">
                <label className="zara-body text-zara-charcoal block">
                  Last Name
                </label>
                <GlassInput
                  type="text"
                  placeholder="Last name"
                  value={formData.lastName}
                  onChange={(e) => handleInputChange('lastName', e.target.value)}
                  className={cn(
                    errors.lastName && "border-red-300 focus:border-red-500"
                  )}
                  disabled={isLoading}
                />
                {errors.lastName && (
                  <p className="zara-caption text-red-600">{errors.lastName}</p>
                )}
              </div>
            </div>

            {/* Email Field */}
            <div className="space-y-2">
              <label className="zara-body text-zara-charcoal block">
                Email Address
              </label>
              <div className="relative">
                <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-zara-dark-gray" />
                <GlassInput
                  type="email"
                  placeholder="Enter your email"
                  value={formData.email}
                  onChange={(e) => handleInputChange('email', e.target.value)}
                  className={cn(
                    "pl-11",
                    errors.email && "border-red-300 focus:border-red-500"
                  )}
                  disabled={isLoading}
                />
              </div>
              {errors.email && (
                <p className="zara-caption text-red-600">{errors.email}</p>
              )}
            </div>

            {/* Password Field */}
            <div className="space-y-2">
              <label className="zara-body text-zara-charcoal block">
                Password
              </label>
              <div className="relative">
                <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-zara-dark-gray" />
                <GlassInput
                  type={showPassword ? "text" : "password"}
                  placeholder="Create a password"
                  value={formData.password}
                  onChange={(e) => handleInputChange('password', e.target.value)}
                  className={cn(
                    "pl-11 pr-11",
                    errors.password && "border-red-300 focus:border-red-500"
                  )}
                  disabled={isLoading}
                />
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="absolute right-3 top-1/2 transform -translate-y-1/2 text-zara-dark-gray hover:text-zara-charcoal transition-colors"
                  disabled={isLoading}
                >
                  {showPassword ? <EyeOff className="w-5 h-5" /> : <Eye className="w-5 h-5" />}
                </button>
              </div>
              
              {/* Password Requirements */}
              {formData.password && (
                <div className="glass-subtle p-3 rounded-lg space-y-1">
                  {passwordRequirements.map((req, index) => (
                    <div key={index} className="flex items-center space-x-2">
                      <Check className={cn(
                        "w-3 h-3",
                        req.met ? "text-green-600" : "text-gray-400"
                      )} />
                      <span className={cn(
                        "zara-caption",
                        req.met ? "text-green-700" : "text-gray-600"
                      )}>
                        {req.text}
                      </span>
                    </div>
                  ))}
                </div>
              )}
              
              {errors.password && (
                <p className="zara-caption text-red-600">{errors.password}</p>
              )}
            </div>

            {/* Confirm Password Field */}
            <div className="space-y-2">
              <label className="zara-body text-zara-charcoal block">
                Confirm Password
              </label>
              <div className="relative">
                <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-zara-dark-gray" />
                <GlassInput
                  type={showConfirmPassword ? "text" : "password"}
                  placeholder="Confirm your password"
                  value={formData.confirmPassword}
                  onChange={(e) => handleInputChange('confirmPassword', e.target.value)}
                  className={cn(
                    "pl-11 pr-11",
                    errors.confirmPassword && "border-red-300 focus:border-red-500"
                  )}
                  disabled={isLoading}
                />
                <button
                  type="button"
                  onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                  className="absolute right-3 top-1/2 transform -translate-y-1/2 text-zara-dark-gray hover:text-zara-charcoal transition-colors"
                  disabled={isLoading}
                >
                  {showConfirmPassword ? <EyeOff className="w-5 h-5" /> : <Eye className="w-5 h-5" />}
                </button>
              </div>
              {errors.confirmPassword && (
                <p className="zara-caption text-red-600">{errors.confirmPassword}</p>
              )}
            </div>

            {/* Submit Button */}
            <GlassButton
              type="submit"
              variant="primary"
              size="lg"
              className="w-full"
              disabled={isLoading}
            >
              {isLoading ? 'Creating Account...' : 'Create Account'}
            </GlassButton>

            {/* Divider */}
            <div className="relative">
              <div className="absolute inset-0 flex items-center">
                <div className="w-full border-t border-zara-medium-gray/30" />
              </div>
              <div className="relative flex justify-center text-sm">
                <span className="px-4 bg-white/80 zara-caption text-zara-dark-gray">
                  Already have an account?
                </span>
              </div>
            </div>

            {/* Sign In Link */}
            <GlassButton
              type="button"
              variant="secondary"
              size="lg"
              className="w-full"
              onClick={onSignIn}
              disabled={isLoading}
            >
              Sign In Instead
            </GlassButton>
          </form>
        </GlassCard>

        {/* Terms Note */}
        <div className="mt-6 text-center">
          <p className="zara-caption text-zara-dark-gray">
            By creating an account, you agree to our Terms of Service and Privacy Policy.
          </p>
        </div>
      </div>
    </div>
  );
};
