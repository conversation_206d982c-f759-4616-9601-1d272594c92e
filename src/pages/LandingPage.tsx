import { Sparkles, Shirt, <PERSON>, Cloud, Shield } from 'lucide-react';
import { GlassCard } from '@/components/GlassCard';
import { GlassButton } from '@/components/ui/glass-button';

interface LandingPageProps {
  onSignUp: () => void;
  onSignIn: () => void;
}

export const LandingPage = ({ onSignUp, onSignIn }: LandingPageProps) => {

  return (
    <div className="min-h-screen bg-gradient-to-br from-zara-white via-zara-light-gray/30 to-zara-white flex items-center justify-center p-6">
      <div className="w-full max-w-6xl mx-auto">
        {/* Main Content */}
        <div className="text-center">
          {/* Logo and Brand */}
          <div className="flex items-center justify-center space-x-4 mb-8">
            <div className="w-16 h-16 rounded-2xl bg-gradient-to-br from-zara-charcoal to-zara-dark-gray flex items-center justify-center">
              <Sparkles className="w-10 h-10 text-white" />
            </div>
            <h1 className="zara-h1 text-zara-charcoal">Closet Glass Chic</h1>
          </div>

          {/* Hero Section */}
          <div className="mb-12">
            <h2 className="zara-h2 text-zara-charcoal mb-6 max-w-4xl mx-auto">
              Your Digital Closet,
              <span className="block bg-gradient-to-r from-zara-charcoal via-zara-dark-gray to-zara-charcoal bg-clip-text text-transparent">
                Reimagined
              </span>
            </h2>
            <p className="zara-body text-zara-dark-gray max-w-2xl mx-auto mb-8">
              Transform your wardrobe management with AI-powered organization, weather-based outfit suggestions,
              and intelligent style analytics. Experience fashion like never before.
            </p>
          </div>

      {/* Hero Section */}
      <section className="pt-32 pb-20 px-6">
        <div className="max-w-7xl mx-auto text-center">
          <div className="mb-8">
            <h1 className="zara-h1 text-zara-charcoal mb-6 max-w-4xl mx-auto">
              Your Digital Closet,
              <span className="block bg-gradient-to-r from-zara-charcoal via-zara-dark-gray to-zara-charcoal bg-clip-text text-transparent">
                Reimagined
              </span>
            </h1>
            <p className="zara-body text-zara-dark-gray max-w-2xl mx-auto mb-8">
              Transform your wardrobe management with AI-powered organization, weather-based outfit suggestions, 
              and intelligent style analytics. Experience fashion like never before.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <GlassButton 
                variant="primary" 
                size="lg" 
                onClick={onGetStarted}
                className="group"
              >
                Start Your Journey
                <ArrowRight className="w-5 h-5 ml-2 group-hover:translate-x-1 transition-transform" />
              </GlassButton>
              <GlassButton variant="secondary" size="lg" onClick={onSignIn}>
                Sign In
              </GlassButton>
            </div>
          </div>

          {/* Hero Visual */}
          <div className="relative max-w-4xl mx-auto">
            <GlassCard variant="hero" className="p-8 relative overflow-hidden">
              <div className="absolute inset-0 bg-gradient-to-br from-blue-500/10 via-purple-500/10 to-pink-500/10" />
              <div className="relative grid grid-cols-2 md:grid-cols-4 gap-4">
                {[...Array(8)].map((_, i) => (
                  <div
                    key={i}
                    className={cn(
                      "aspect-square rounded-xl bg-gradient-to-br opacity-60 hover:opacity-100 transition-all duration-500",
                      i % 4 === 0 && "from-blue-200 to-blue-300",
                      i % 4 === 1 && "from-purple-200 to-purple-300",
                      i % 4 === 2 && "from-pink-200 to-pink-300",
                      i % 4 === 3 && "from-green-200 to-green-300"
                    )}
                    style={{
                      animationDelay: `${i * 100}ms`,
                      animation: 'fadeInUp 0.6s ease-out forwards'
                    }}
                  />
                ))}
              </div>
            </GlassCard>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 px-6">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="zara-h2 text-zara-charcoal mb-4">
              Everything You Need for Perfect Style
            </h2>
            <p className="zara-body text-zara-dark-gray max-w-2xl mx-auto">
              Discover powerful features designed to revolutionize how you interact with your wardrobe.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {features.map((feature, index) => (
              <GlassCard
                key={index}
                variant="medium"
                morphOnHover
                className={cn(
                  "p-6 cursor-pointer transition-all duration-500 relative overflow-hidden",
                  hoveredFeature === index && "scale-105"
                )}
                onMouseEnter={() => setHoveredFeature(index)}
                onMouseLeave={() => setHoveredFeature(null)}
              >
                <div className={cn(
                  "absolute inset-0 bg-gradient-to-br opacity-0 transition-opacity duration-500",
                  feature.gradient,
                  hoveredFeature === index && "opacity-100"
                )} />
                <div className="relative">
                  <div className="w-12 h-12 rounded-xl bg-gradient-to-br from-zara-charcoal to-zara-dark-gray flex items-center justify-center mb-4">
                    <feature.icon className="w-6 h-6 text-white" />
                  </div>
                  <h3 className="zara-h4 text-zara-charcoal mb-2">{feature.title}</h3>
                  <p className="zara-caption text-zara-dark-gray">{feature.description}</p>
                </div>
              </GlassCard>
            ))}
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-20 px-6 bg-gradient-to-r from-zara-light-gray/50 to-zara-white">
        <div className="max-w-7xl mx-auto">
          <GlassCard variant="prominent" className="p-12">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8 text-center">
              <div>
                <div className="flex items-center justify-center mb-4">
                  <Users className="w-8 h-8 text-zara-charcoal" />
                </div>
                <div className="zara-h2 text-zara-charcoal mb-2">10,000+</div>
                <div className="zara-body text-zara-dark-gray">Happy Users</div>
              </div>
              <div>
                <div className="flex items-center justify-center mb-4">
                  <Shirt className="w-8 h-8 text-zara-charcoal" />
                </div>
                <div className="zara-h2 text-zara-charcoal mb-2">500K+</div>
                <div className="zara-body text-zara-dark-gray">Items Organized</div>
              </div>
              <div>
                <div className="flex items-center justify-center mb-4">
                  <Zap className="w-8 h-8 text-zara-charcoal" />
                </div>
                <div className="zara-h2 text-zara-charcoal mb-2">99.9%</div>
                <div className="zara-body text-zara-dark-gray">Uptime</div>
              </div>
            </div>
          </GlassCard>
        </div>
      </section>

      {/* Testimonials Section */}
      <section className="py-20 px-6">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="zara-h2 text-zara-charcoal mb-4">
              Loved by Fashion Enthusiasts
            </h2>
            <p className="zara-body text-zara-dark-gray max-w-2xl mx-auto">
              See what our users are saying about their experience with Closet Glass Chic.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {testimonials.map((testimonial, index) => (
              <GlassCard key={index} variant="light" className="p-6">
                <div className="flex items-center mb-4">
                  {[...Array(testimonial.rating)].map((_, i) => (
                    <Star key={i} className="w-4 h-4 text-yellow-500 fill-current" />
                  ))}
                </div>
                <p className="zara-body text-zara-dark-gray mb-4 italic">
                  "{testimonial.content}"
                </p>
                <div>
                  <div className="zara-h5 text-zara-charcoal">{testimonial.name}</div>
                  <div className="zara-caption text-zara-dark-gray">{testimonial.role}</div>
                </div>
              </GlassCard>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 px-6">
        <div className="max-w-4xl mx-auto text-center">
          <GlassCard variant="hero" className="p-12 relative overflow-hidden">
            <div className="absolute inset-0 bg-gradient-to-br from-blue-500/10 via-purple-500/10 to-pink-500/10" />
            <div className="relative">
              <h2 className="zara-h2 text-zara-charcoal mb-4">
                Ready to Transform Your Wardrobe?
              </h2>
              <p className="zara-body text-zara-dark-gray mb-8 max-w-2xl mx-auto">
                Join thousands of users who have already revolutionized their style with our intelligent wardrobe management system.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <GlassButton 
                  variant="primary" 
                  size="lg" 
                  onClick={onGetStarted}
                  className="group"
                >
                  Get Started Free
                  <ArrowRight className="w-5 h-5 ml-2 group-hover:translate-x-1 transition-transform" />
                </GlassButton>
                <GlassButton variant="secondary" size="lg">
                  <Shield className="w-5 h-5 mr-2" />
                  100% Secure & Private
                </GlassButton>
              </div>
            </div>
          </GlassCard>
        </div>
      </section>

      {/* Footer */}
      <footer className="py-12 px-6 border-t border-zara-medium-gray/30">
        <div className="max-w-7xl mx-auto text-center">
          <div className="flex items-center justify-center space-x-3 mb-4">
            <div className="w-8 h-8 rounded-lg bg-gradient-to-br from-zara-charcoal to-zara-dark-gray flex items-center justify-center">
              <Sparkles className="w-5 h-5 text-white" />
            </div>
            <span className="zara-h4 text-zara-charcoal">Closet Glass Chic</span>
          </div>
          <p className="zara-caption text-zara-dark-gray">
            © 2024 Closet Glass Chic. All rights reserved. Made with ❤️ for fashion lovers.
          </p>
        </div>
      </footer>
    </div>
  );
};
