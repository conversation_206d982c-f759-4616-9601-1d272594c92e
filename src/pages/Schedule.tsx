import { useState, useEffect } from 'react';
import { Calendar, Sun, Cloud, CloudRain, Edit3, Clock, Plus, ChevronLeft, ChevronRight, MapPin } from 'lucide-react';
import { GlassCard } from '@/components/GlassCard';
import { GlassButton } from '@/components/ui/glass-button';
import { cn } from '@/lib/utils';

interface ScheduledOutfit {
  id: string;
  date: string;
  day: string;
  weather: {
    icon: React.ElementType;
    temp: string;
    condition: string;
  };
  outfit?: {
    name: string;
    items: string[];
  };
}

export const Schedule = () => {
  const [selectedDate, setSelectedDate] = useState<string | null>(null);
  const [viewMode, setViewMode] = useState<'daily' | 'calendar'>('daily');
  const [isLoaded, setIsLoaded] = useState(false);
  const [currentWeek, setCurrentWeek] = useState(0);

  const scheduledOutfits: ScheduledOutfit[] = [];

  useEffect(() => {
    const timer = setTimeout(() => setIsLoaded(true), 100);
    return () => clearTimeout(timer);
  }, []);

  // Generate upcoming days for the schedule
  const generateUpcomingDays = () => {
    const days = [];
    const today = new Date();

    for (let i = 0; i < 7; i++) {
      const date = new Date(today);
      date.setDate(today.getDate() + i + (currentWeek * 7));

      days.push({
        id: `day-${i}`,
        date: date.toISOString().split('T')[0],
        day: date.toLocaleDateString('en-US', { weekday: 'long' }),
        shortDay: date.toLocaleDateString('en-US', { weekday: 'short' }),
        dayNumber: date.getDate(),
        weather: {
          icon: i % 3 === 0 ? Sun : i % 3 === 1 ? Cloud : CloudRain,
          temp: `${20 + Math.floor(Math.random() * 10)}°`,
          condition: i % 3 === 0 ? 'Sunny' : i % 3 === 1 ? 'Cloudy' : 'Rainy'
        }
      });
    }

    return days;
  };

  const upcomingDays = generateUpcomingDays();

  return (
    <div className="min-h-screen bg-zara-white relative overflow-hidden">
      {/* Subtle background pattern */}
      <div className="absolute inset-0 opacity-[0.02]">
        <div className="w-full h-full" style={{
          backgroundImage: `radial-gradient(circle at 25% 25%, hsl(var(--zara-charcoal)) 1px, transparent 1px)`,
          backgroundSize: '60px 60px'
        }} />
      </div>

      {/* Header */}
      <div className={cn(
        "sticky top-0 z-30 bg-zara-white/80 backdrop-blur-xl border-b border-zara-light-gray transition-all duration-800 ease-liquid",
        isLoaded ? "opacity-100 translate-y-0" : "opacity-0 -translate-y-4"
      )}>
        <div className="px-6 pt-16 pb-6">
          <div className="flex items-center justify-between mb-6">
            <h1 className="zara-hero">Schedule</h1>

            {/* Enhanced View Toggle */}
            <GlassCard variant="subtle" className="p-1">
              <div className="flex">
                <button
                  onClick={() => setViewMode('daily')}
                  className={cn(
                    "px-4 py-2 rounded-xl transition-all duration-300 ease-glass",
                    viewMode === 'daily'
                      ? 'glass-strong text-zara-charcoal scale-105'
                      : 'text-zara-dark-gray hover:glass-light hover:text-zara-charcoal'
                  )}
                >
                  <span className="zara-body font-medium">Daily</span>
                </button>
                <button
                  onClick={() => setViewMode('calendar')}
                  className={cn(
                    "px-4 py-2 rounded-xl transition-all duration-300 ease-glass flex items-center",
                    viewMode === 'calendar'
                      ? 'glass-strong text-zara-charcoal scale-105'
                      : 'text-zara-dark-gray hover:glass-light hover:text-zara-charcoal'
                  )}
                >
                  <Calendar size={16} />
                </button>
              </div>
            </GlassCard>
          </div>

          {/* Week Navigation */}
          {viewMode === 'daily' && (
            <div className="flex items-center justify-between">
              <button
                onClick={() => setCurrentWeek(Math.max(0, currentWeek - 1))}
                className="p-2 glass-subtle rounded-xl hover:glass-light transition-all duration-300"
                disabled={currentWeek === 0}
              >
                <ChevronLeft size={20} className={currentWeek === 0 ? 'text-zara-medium-gray' : 'text-zara-charcoal'} />
              </button>

              <div className="flex items-center space-x-2">
                <Clock size={16} className="text-zara-dark-gray" />
                <span className="zara-body text-zara-dark-gray">
                  {currentWeek === 0 ? 'This Week' : `Week ${currentWeek + 1}`}
                </span>
              </div>

              <button
                onClick={() => setCurrentWeek(currentWeek + 1)}
                className="p-2 glass-subtle rounded-xl hover:glass-light transition-all duration-300"
              >
                <ChevronRight size={20} className="text-zara-charcoal" />
              </button>
            </div>
          )}
        </div>
      </div>

      {/* Content */}
      <div className={cn(
        "relative z-10 transition-all duration-1000 ease-liquid",
        isLoaded ? "opacity-100 translate-y-0" : "opacity-0 translate-y-8"
      )}>

        {/* Content */}
        <div className="pb-32">
          {viewMode === 'daily' ? (
            // Daily view
            scheduledOutfits.length > 0 ? (
              <div className="space-y-4">
                {scheduledOutfits.map((item) => {
                  const WeatherIcon = item.weather.icon;
                  return (
                    <GlassCard key={item.date} className="p-6">
                      <div className="space-y-4">
                        <div className="flex items-center justify-between">
                          <div>
                            <h3 className="zara-subtitle">{item.day}</h3>
                            <p className="zara-body text-muted-foreground">
                              {new Date(item.date).toLocaleDateString('en-US', { 
                                month: 'long', 
                                day: 'numeric' 
                              })}
                            </p>
                          </div>
                          <div className="flex items-center space-x-3">
                            <div className="text-right">
                              <div className="flex items-center space-x-1">
                                <WeatherIcon size={16} className="text-muted-foreground" />
                                <span className="zara-body">{item.weather.temp}</span>
                              </div>
                              <p className="zara-body text-muted-foreground">{item.weather.condition}</p>
                            </div>
                            <button className="p-2 glass-subtle rounded-lg hover:bg-white/30 transition-colors">
                              <Edit3 size={16} className="text-muted-foreground" />
                            </button>
                          </div>
                        </div>
                        {item.outfit ? (
                          <div className="space-y-3">
                            <h4 className="zara-body font-medium">{item.outfit.name}</h4>
                            <div className="flex items-center space-x-2">
                              {item.outfit.items.map((clothingItem: string, index: number) => (
                                <span key={index} className="zara-body text-muted-foreground">
                                  {clothingItem}
                                  {index < item.outfit!.items.length - 1 && ' •'}
                                </span>
                              ))}
                            </div>
                            <div className="flex space-x-2">
                              {[1, 2, 3].map((preview) => (
                                <div 
                                  key={preview}
                                  className="w-10 h-10 glass-subtle rounded-lg flex items-center justify-center"
                                >
                                  <div className="w-6 h-6 bg-gradient-to-br from-muted to-muted-foreground/20 rounded" />
                                </div>
                              ))}
                            </div>
                          </div>
                        ) : (
                          <div className="py-8 text-center">
                            <p className="zara-body text-muted-foreground mb-4">No outfit planned</p>
                            <GlassButton variant="primary" size="sm">
                              Plan Outfit
                            </GlassButton>
                          </div>
                        )}
                      </div>
                    </GlassCard>
                  );
                })}
              </div>
            ) : (
              <GlassCard className="p-8">
                <div className="text-center space-y-4">
                  <div className="w-16 h-16 mx-auto glass-subtle rounded-full flex items-center justify-center">
                    <Calendar size={32} className="text-muted-foreground" />
                  </div>
                  <div>
                    <h3 className="zara-subtitle mb-2">No Outfits Scheduled</h3>
                    <p className="zara-body text-muted-foreground mb-6">
                      Plan your outfits ahead of time to match the weather and your schedule
                    </p>
                    <GlassButton variant="primary">
                      Plan Your First Outfit
                    </GlassButton>
                  </div>
                </div>
              </GlassCard>
            )
          ) : (
            // Calendar view
            <GlassCard className="p-6">
              <div className="text-center py-12">
                <Calendar size={48} className="mx-auto text-muted-foreground mb-4" />
                <h3 className="zara-subtitle mb-2">Calendar View</h3>
                <p className="zara-body text-muted-foreground">
                  Calendar integration coming soon
                </p>
              </div>
            </GlassCard>
          )}
        </div>
      </div>
    </div>
  );
};