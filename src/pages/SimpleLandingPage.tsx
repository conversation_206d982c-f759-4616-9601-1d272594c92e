import { Sparkles, Shirt, <PERSON>, Cloud, Shield } from 'lucide-react';
import { GlassCard } from '@/components/GlassCard';
import { GlassButton } from '@/components/ui/glass-button';

interface SimpleLandingPageProps {
  onSignUp: () => void;
  onSignIn: () => void;
}

export const SimpleLandingPage = ({ onSignUp, onSignIn }: SimpleLandingPageProps) => {
  return (
    <div className="min-h-screen bg-gradient-to-br from-zara-white via-zara-light-gray/30 to-zara-white flex items-center justify-center p-6">
      <div className="w-full max-w-4xl mx-auto">
        {/* Main Content */}
        <div className="text-center">
          {/* Logo and Brand */}
          <div className="flex items-center justify-center space-x-4 mb-8">
            <div className="w-16 h-16 rounded-2xl bg-gradient-to-br from-zara-charcoal to-zara-dark-gray flex items-center justify-center">
              <Sparkles className="w-10 h-10 text-white" />
            </div>
            <h1 className="zara-h1 text-zara-charcoal">Closet Glass Chic</h1>
          </div>

          {/* Hero Section */}
          <div className="mb-12">
            <h2 className="zara-h2 text-zara-charcoal mb-6 max-w-3xl mx-auto">
              Your Digital Closet,
              <span className="block bg-gradient-to-r from-zara-charcoal via-zara-dark-gray to-zara-charcoal bg-clip-text text-transparent">
                Reimagined
              </span>
            </h2>
            <p className="zara-body text-zara-dark-gray max-w-2xl mx-auto mb-8">
              Transform your wardrobe management with AI-powered organization, weather-based outfit suggestions, 
              and intelligent style analytics. Experience fashion like never before.
            </p>
          </div>

          {/* Key Features Preview */}
          <div className="mb-12">
            <GlassCard variant="hero" className="p-8 relative overflow-hidden">
              <div className="absolute inset-0 bg-gradient-to-br from-blue-500/10 via-purple-500/10 to-pink-500/10" />
              <div className="relative grid grid-cols-2 md:grid-cols-4 gap-6">
                <div className="text-center">
                  <div className="w-12 h-12 rounded-xl bg-gradient-to-br from-blue-500 to-blue-600 flex items-center justify-center mx-auto mb-3">
                    <Shirt className="w-6 h-6 text-white" />
                  </div>
                  <h3 className="zara-h5 text-zara-charcoal mb-1">Smart Wardrobe</h3>
                  <p className="zara-caption text-zara-dark-gray">AI-powered organization</p>
                </div>
                <div className="text-center">
                  <div className="w-12 h-12 rounded-xl bg-gradient-to-br from-green-500 to-green-600 flex items-center justify-center mx-auto mb-3">
                    <Cloud className="w-6 h-6 text-white" />
                  </div>
                  <h3 className="zara-h5 text-zara-charcoal mb-1">Weather Outfits</h3>
                  <p className="zara-caption text-zara-dark-gray">Perfect for any weather</p>
                </div>
                <div className="text-center">
                  <div className="w-12 h-12 rounded-xl bg-gradient-to-br from-purple-500 to-purple-600 flex items-center justify-center mx-auto mb-3">
                    <Calendar className="w-6 h-6 text-white" />
                  </div>
                  <h3 className="zara-h5 text-zara-charcoal mb-1">Outfit Planning</h3>
                  <p className="zara-caption text-zara-dark-gray">Plan ahead with ease</p>
                </div>
                <div className="text-center">
                  <div className="w-12 h-12 rounded-xl bg-gradient-to-br from-orange-500 to-orange-600 flex items-center justify-center mx-auto mb-3">
                    <Sparkles className="w-6 h-6 text-white" />
                  </div>
                  <h3 className="zara-h5 text-zara-charcoal mb-1">Style Analytics</h3>
                  <p className="zara-caption text-zara-dark-gray">Discover your style</p>
                </div>
              </div>
            </GlassCard>
          </div>

          {/* Call to Action */}
          <div className="mb-8">
            <GlassCard variant="prominent" className="p-8">
              <h3 className="zara-h3 text-zara-charcoal mb-4">Ready to Get Started?</h3>
              <p className="zara-body text-zara-dark-gray mb-6 max-w-xl mx-auto">
                Join thousands of fashion enthusiasts who have already transformed their style with our intelligent wardrobe management system.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <GlassButton 
                  variant="primary" 
                  size="lg" 
                  onClick={onSignUp}
                  className="group"
                >
                  Sign Up & Get Started
                </GlassButton>
                <GlassButton variant="secondary" size="lg" onClick={onSignIn}>
                  Sign In
                </GlassButton>
              </div>
            </GlassCard>
          </div>

          {/* Security Note */}
          <div className="text-center">
            <div className="flex items-center justify-center space-x-2 text-zara-dark-gray">
              <Shield className="w-4 h-4" />
              <span className="zara-caption">
                100% Secure & Private - Your data is encrypted and never shared
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
