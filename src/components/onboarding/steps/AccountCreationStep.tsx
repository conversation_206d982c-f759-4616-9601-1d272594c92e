import { useState } from 'react';
import { Mail, Lock, Eye, EyeOff, Shield } from 'lucide-react';
import { GlassCard } from '@/components/GlassCard';
import { GlassButton } from '@/components/ui/glass-button';
import { GlassInput } from '@/components/ui/glass-input';
import { cn } from '@/lib/utils';
import { UserProfileFormErrors } from '@/types/user';
import PersistentAuthService from '@/services/persistentAuth';

interface AccountCreationData {
  email: string;
  password: string;
  confirmPassword: string;
}

interface AccountCreationStepProps {
  data: AccountCreationData;
  errors: UserProfileFormErrors;
  onDataChange: (data: AccountCreationData) => void;
  onComplete: () => void;
  onPrevious: () => void;
  currentStep: number;
  totalSteps: number;
  isLoading?: boolean;
}

export const AccountCreationStep = ({
  data,
  errors,
  onDataChange,
  onComplete,
  onPrevious,
  currentStep,
  totalSteps,
  isLoading = false
}: AccountCreationStepProps) => {
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [passwordStrength, setPasswordStrength] = useState<{
    score: number;
    feedback: string[];
  }>({ score: 0, feedback: [] });

  const handleInputChange = (field: keyof AccountCreationData, value: string) => {
    const newData = {
      ...data,
      [field]: value
    };
    
    onDataChange(newData);

    // Update password strength when password changes
    if (field === 'password') {
      const validation = PersistentAuthService.validatePassword(value);
      setPasswordStrength({
        score: validation.isValid ? 4 : Math.min(3, value.length / 3),
        feedback: validation.errors
      });
    }
  };

  const getPasswordStrengthColor = (score: number) => {
    if (score < 2) return 'bg-red-500';
    if (score < 3) return 'bg-yellow-500';
    if (score < 4) return 'bg-blue-500';
    return 'bg-green-500';
  };

  const getPasswordStrengthText = (score: number) => {
    if (score < 2) return 'Weak';
    if (score < 3) return 'Fair';
    if (score < 4) return 'Good';
    return 'Strong';
  };

  const validateAndComplete = () => {
    // Basic validation will be handled by parent component
    onComplete();
  };

  return (
    <div className="min-h-screen flex items-center justify-center p-6 bg-gradient-to-br from-zara-white via-zara-light-gray/30 to-zara-white">
      <div className="w-full max-w-md">
        {/* Progress indicator */}
        <div className="text-center mb-8">
          <p className="zara-caption text-zara-dark-gray mb-4">
            Step {currentStep} of {totalSteps}
          </p>
          <div className="w-full bg-zara-light-gray/40 rounded-full h-2">
            <div 
              className="bg-gradient-to-r from-zara-charcoal to-zara-dark-gray h-2 rounded-full transition-all duration-500"
              style={{ width: `${(currentStep / totalSteps) * 100}%` }}
            />
          </div>
        </div>

        <GlassCard className="p-8 space-y-6">
          {/* Header */}
          <div className="text-center space-y-3">
            <div className="w-16 h-16 mx-auto glass-subtle rounded-full flex items-center justify-center">
              <Shield size={24} className="text-zara-charcoal" />
            </div>
            <h1 className="zara-title">Create Your Account</h1>
            <p className="zara-body text-zara-dark-gray">
              Set up your account to save your preferences and access all features
            </p>
          </div>

          {/* Form fields */}
          <div className="space-y-4">
            {/* Email */}
            <div>
              <label className="block zara-body text-zara-dark-gray mb-2">
                Email Address *
              </label>
              <div className="relative">
                <GlassInput
                  type="email"
                  value={data.email}
                  onChange={(e) => handleInputChange('email', e.target.value)}
                  placeholder="Enter your email address"
                  className={cn(
                    errors.email ? 'border-red-300' : '',
                    "pl-12"
                  )}
                />
                <div className="absolute left-4 top-1/2 transform -translate-y-1/2">
                  <Mail size={20} className="text-zara-dark-gray" />
                </div>
              </div>
              {errors.email && (
                <p className="text-red-500 zara-caption mt-1">{errors.email}</p>
              )}
            </div>

            {/* Password */}
            <div>
              <label className="block zara-body text-zara-dark-gray mb-2">
                Password *
              </label>
              <div className="relative">
                <GlassInput
                  type={showPassword ? 'text' : 'password'}
                  value={data.password}
                  onChange={(e) => handleInputChange('password', e.target.value)}
                  placeholder="Create a strong password"
                  className={cn(
                    errors.password ? 'border-red-300' : '',
                    "pl-12 pr-12"
                  )}
                />
                <div className="absolute left-4 top-1/2 transform -translate-y-1/2">
                  <Lock size={20} className="text-zara-dark-gray" />
                </div>
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="absolute right-4 top-1/2 transform -translate-y-1/2 text-zara-dark-gray hover:text-zara-charcoal"
                >
                  {showPassword ? <EyeOff size={20} /> : <Eye size={20} />}
                </button>
              </div>
              
              {/* Password strength indicator */}
              {data.password && (
                <div className="mt-2">
                  <div className="flex items-center space-x-2 mb-1">
                    <div className="flex-1 bg-zara-light-gray/40 rounded-full h-1">
                      <div 
                        className={cn(
                          "h-1 rounded-full transition-all duration-300",
                          getPasswordStrengthColor(passwordStrength.score)
                        )}
                        style={{ width: `${(passwordStrength.score / 4) * 100}%` }}
                      />
                    </div>
                    <span className="zara-caption text-zara-dark-gray">
                      {getPasswordStrengthText(passwordStrength.score)}
                    </span>
                  </div>
                  {passwordStrength.feedback.length > 0 && (
                    <div className="space-y-1">
                      {passwordStrength.feedback.map((feedback, index) => (
                        <p key={index} className="text-red-500 zara-caption">
                          • {feedback}
                        </p>
                      ))}
                    </div>
                  )}
                </div>
              )}
              
              {errors.password && (
                <p className="text-red-500 zara-caption mt-1">{errors.password}</p>
              )}
            </div>

            {/* Confirm Password */}
            <div>
              <label className="block zara-body text-zara-dark-gray mb-2">
                Confirm Password *
              </label>
              <div className="relative">
                <GlassInput
                  type={showConfirmPassword ? 'text' : 'password'}
                  value={data.confirmPassword}
                  onChange={(e) => handleInputChange('confirmPassword', e.target.value)}
                  placeholder="Confirm your password"
                  className={cn(
                    errors.confirmPassword ? 'border-red-300' : '',
                    "pl-12 pr-12"
                  )}
                />
                <div className="absolute left-4 top-1/2 transform -translate-y-1/2">
                  <Lock size={20} className="text-zara-dark-gray" />
                </div>
                <button
                  type="button"
                  onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                  className="absolute right-4 top-1/2 transform -translate-y-1/2 text-zara-dark-gray hover:text-zara-charcoal"
                >
                  {showConfirmPassword ? <EyeOff size={20} /> : <Eye size={20} />}
                </button>
              </div>
              {errors.confirmPassword && (
                <p className="text-red-500 zara-caption mt-1">{errors.confirmPassword}</p>
              )}
            </div>
          </div>

          {/* Security note */}
          <div className="glass-subtle p-4 rounded-xl">
            <div className="flex items-start space-x-3">
              <Shield size={16} className="text-zara-dark-gray mt-0.5 flex-shrink-0" />
              <div>
                <p className="zara-caption text-zara-dark-gray">
                  Your password is encrypted and stored securely. We never share your personal information with third parties.
                </p>
              </div>
            </div>
          </div>

          {/* Navigation buttons */}
          <div className="flex gap-3 pt-4">
            <GlassButton
              variant="secondary"
              onClick={onPrevious}
              className="flex-1"
              disabled={isLoading}
            >
              Previous
            </GlassButton>
            <GlassButton
              variant="primary"
              onClick={validateAndComplete}
              className="flex-1"
              disabled={isLoading}
            >
              {isLoading ? 'Creating Account...' : 'Complete'}
            </GlassButton>
          </div>
        </GlassCard>
      </div>
    </div>
  );
};
