import { useState, useEffect } from 'react';
import { MapPin, Search, Loader2 } from 'lucide-react';
import { GlassCard } from '@/components/GlassCard';
import { GlassButton } from '@/components/ui/glass-button';
import { GlassInput } from '@/components/ui/glass-input';
import { cn } from '@/lib/utils';
import { UserProfileFormErrors, CitySearchResult, WeatherData } from '@/types/user';
import { searchCities, getWeatherData, debounce } from '@/services/api';

interface LocationData {
  cityName: string;
  selectedCity?: CitySearchResult;
}

interface LocationStepProps {
  data: LocationData;
  errors: UserProfileFormErrors;
  onDataChange: (data: LocationData) => void;
  onNext: () => void;
  onPrevious: () => void;
  currentStep: number;
  totalSteps: number;
}

export const LocationStep = ({
  data,
  errors,
  onDataChange,
  onNext,
  onPrevious,
  currentStep,
  totalSteps
}: LocationStepProps) => {
  const [citySearchResults, setCitySearchResults] = useState<CitySearchResult[]>([]);
  const [showCityDropdown, setShowCityDropdown] = useState(false);
  const [isSearchingCities, setIsSearchingCities] = useState(false);
  const [weatherData, setWeatherData] = useState<WeatherData | null>(null);
  const [isLoadingWeather, setIsLoadingWeather] = useState(false);

  // Debounced city search
  const debouncedCitySearch = debounce(async (query: string) => {
    if (query.length < 2) {
      setCitySearchResults([]);
      setShowCityDropdown(false);
      return;
    }

    setIsSearchingCities(true);
    try {
      const results = await searchCities(query);
      setCitySearchResults(results);
      setShowCityDropdown(results.length > 0);
    } catch (error) {
      console.error('City search error:', error);
      setCitySearchResults([]);
      setShowCityDropdown(false);
    } finally {
      setIsSearchingCities(false);
    }
  }, 300);

  const handleCityInputChange = (value: string) => {
    onDataChange({
      ...data,
      cityName: value,
      selectedCity: undefined
    });
    debouncedCitySearch(value);
  };

  const handleCitySelect = async (city: CitySearchResult) => {
    onDataChange({
      cityName: city.formattedName,
      selectedCity: city
    });
    setShowCityDropdown(false);
    setCitySearchResults([]);

    // Load weather data for confirmation
    if (import.meta.env.VITE_ENABLE_WEATHER_INTEGRATION === 'true') {
      setIsLoadingWeather(true);
      try {
        const weather = await getWeatherData(parseFloat(city.lat), parseFloat(city.lon));
        setWeatherData(weather);
      } catch (error) {
        console.warn('Weather data failed to load:', error);
        setWeatherData(null);
      } finally {
        setIsLoadingWeather(false);
      }
    }
  };

  const validateAndNext = () => {
    // Basic validation will be handled by parent component
    onNext();
  };

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = () => {
      setShowCityDropdown(false);
    };

    if (showCityDropdown) {
      document.addEventListener('click', handleClickOutside);
      return () => document.removeEventListener('click', handleClickOutside);
    }
  }, [showCityDropdown]);

  return (
    <div className="min-h-screen flex items-center justify-center p-6 bg-gradient-to-br from-zara-white via-zara-light-gray/30 to-zara-white">
      <div className="w-full max-w-md">
        {/* Progress indicator */}
        <div className="text-center mb-8">
          <p className="zara-caption text-zara-dark-gray mb-4">
            Step {currentStep} of {totalSteps}
          </p>
          <div className="w-full bg-zara-light-gray/40 rounded-full h-2">
            <div 
              className="bg-gradient-to-r from-zara-charcoal to-zara-dark-gray h-2 rounded-full transition-all duration-500"
              style={{ width: `${(currentStep / totalSteps) * 100}%` }}
            />
          </div>
        </div>

        <GlassCard className="p-8 space-y-6">
          {/* Header */}
          <div className="text-center space-y-3">
            <div className="w-16 h-16 mx-auto glass-subtle rounded-full flex items-center justify-center">
              <MapPin size={24} className="text-zara-charcoal" />
            </div>
            <h1 className="zara-title">Your Location</h1>
            <p className="zara-body text-zara-dark-gray">
              Where are you located? This helps us provide weather-based outfit recommendations
            </p>
          </div>

          {/* Form field */}
          <div className="space-y-4">
            <div>
              <label className="block zara-body text-zara-dark-gray mb-2">
                City *
              </label>
              <div className="relative" onClick={(e) => e.stopPropagation()}>
                <div className="relative">
                  <GlassInput
                    value={data.cityName}
                    onChange={(e) => handleCityInputChange(e.target.value)}
                    placeholder="Search for your city..."
                    className={cn(
                      errors.cityName ? 'border-red-300' : '',
                      "pl-12"
                    )}
                    onFocus={() => data.cityName.length >= 2 && setShowCityDropdown(citySearchResults.length > 0)}
                  />
                  <div className="absolute left-4 top-1/2 transform -translate-y-1/2">
                    {isSearchingCities ? (
                      <Loader2 size={20} className="text-zara-dark-gray animate-spin" />
                    ) : (
                      <Search size={20} className="text-zara-dark-gray" />
                    )}
                  </div>
                </div>

                {/* City dropdown */}
                {showCityDropdown && citySearchResults.length > 0 && (
                  <div className="absolute top-full left-0 right-0 z-50 mt-1">
                    <GlassCard className="max-h-48 overflow-y-auto">
                      {citySearchResults.map((city) => (
                        <button
                          key={city.place_id}
                          onClick={() => handleCitySelect(city)}
                          className="w-full p-3 text-left hover:glass-light transition-all duration-200 flex items-center space-x-3"
                        >
                          <MapPin size={16} className="text-zara-dark-gray flex-shrink-0" />
                          <span className="zara-body">{city.formattedName}</span>
                        </button>
                      ))}
                    </GlassCard>
                  </div>
                )}
              </div>
              {errors.cityName && (
                <p className="text-red-500 zara-caption mt-1">{errors.cityName}</p>
              )}
            </div>

            {/* Weather confirmation */}
            {weatherData && (
              <GlassCard className="p-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className="w-10 h-10 glass-subtle rounded-full flex items-center justify-center">
                      <MapPin size={16} className="text-zara-charcoal" />
                    </div>
                    <div>
                      <p className="zara-subtitle">Location confirmed</p>
                      <p className="zara-body text-zara-dark-gray">
                        {weatherData.name}, {weatherData.sys.country}
                      </p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="zara-subtitle">{Math.round(weatherData.main.temp)}°C</p>
                    <p className="zara-caption text-zara-dark-gray">
                      {weatherData.weather[0].description}
                    </p>
                  </div>
                </div>
              </GlassCard>
            )}

            {isLoadingWeather && (
              <div className="flex items-center justify-center py-4">
                <Loader2 size={20} className="animate-spin text-zara-dark-gray mr-2" />
                <span className="zara-body text-zara-dark-gray">Loading weather data...</span>
              </div>
            )}
          </div>

          {/* Navigation buttons */}
          <div className="flex gap-3 pt-4">
            <GlassButton
              variant="secondary"
              onClick={onPrevious}
              className="flex-1"
            >
              Previous
            </GlassButton>
            <GlassButton
              variant="primary"
              onClick={validateAndNext}
              className="flex-1"
            >
              Next
            </GlassButton>
          </div>
        </GlassCard>
      </div>
    </div>
  );
};
