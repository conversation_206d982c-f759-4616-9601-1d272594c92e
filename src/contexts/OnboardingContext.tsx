import React, { createContext, useContext, useReducer, useEffect, ReactNode } from 'react';
import { UserProfileFormData } from '@/types/user';

// Types based on design document
export interface OnboardingState {
  isCompleted: boolean;
  currentStep: number;
  skippedSteps: string[];
  completedAt?: Date;
  userProfile?: UserProfileFormData;
  userPreferences: {
    enableNotifications: boolean;
    preferredStyle: string;
    weatherLocation: string;
  };
}

export interface OnboardingStep {
  id: string;
  title: string;
  description: string;
  illustration: string;
  canSkip: boolean;
  action?: () => Promise<void>;
}

// Action types for reducer
type OnboardingAction =
  | { type: 'SET_CURRENT_STEP'; payload: number }
  | { type: 'NEXT_STEP' }
  | { type: 'PREVIOUS_STEP' }
  | { type: 'SKIP_STEP'; payload: string }
  | { type: 'COMPLETE_ONBOARDING' }
  | { type: 'RESET_ONBOARDING' }
  | { type: 'UPDATE_USER_PREFERENCES'; payload: Partial<OnboardingState['userPreferences']> }
  | { type: 'UPDATE_USER_PROFILE'; payload: UserProfileFormData }
  | { type: 'LOAD_STATE'; payload: OnboardingState };

// Context type
interface OnboardingContextType {
  state: OnboardingState;
  steps: OnboardingStep[];
  dispatch: React.Dispatch<OnboardingAction>;
  nextStep: () => void;
  previousStep: () => void;
  skipStep: () => void;
  completeOnboarding: () => void;
  resetOnboarding: () => void;
  updateUserPreferences: (preferences: Partial<OnboardingState['userPreferences']>) => void;
  updateUserProfile: (profile: UserProfileFormData) => void;
  getCurrentStep: () => OnboardingStep | undefined;
  canGoNext: () => boolean;
  canGoPrevious: () => boolean;
}

// Default onboarding steps configuration
const DEFAULT_STEPS: OnboardingStep[] = [
  {
    id: 'welcome',
    title: 'Welcome to Your Fashion Assistant',
    description: 'Discover a new way to organize your wardrobe and plan your outfits with style.',
    illustration: 'welcome-illustration',
    canSkip: false,
  },
  {
    id: 'personal-info',
    title: 'Personal Information',
    description: 'Let\'s start with some basic information about you.',
    illustration: 'personal-info-illustration',
    canSkip: false,
  },
  {
    id: 'date-of-birth',
    title: 'Date of Birth',
    description: 'When were you born? This helps us personalize your experience.',
    illustration: 'date-of-birth-illustration',
    canSkip: false,
  },
  {
    id: 'location',
    title: 'Your Location',
    description: 'Where are you located? This helps us provide weather-based outfit recommendations.',
    illustration: 'location-illustration',
    canSkip: false,
  },
  {
    id: 'account-creation',
    title: 'Create Your Account',
    description: 'Set up your account to save your preferences and access all features.',
    illustration: 'account-illustration',
    canSkip: false,
  },
];

// Initial state
const initialState: OnboardingState = {
  isCompleted: false,
  currentStep: 0,
  skippedSteps: [],
  userPreferences: {
    enableNotifications: false,
    preferredStyle: 'casual',
    weatherLocation: '',
  },
};

// Local storage key
const STORAGE_KEY = 'onboarding-state';

// Reducer function
function onboardingReducer(state: OnboardingState, action: OnboardingAction): OnboardingState {
  switch (action.type) {
    case 'SET_CURRENT_STEP':
      return {
        ...state,
        currentStep: Math.max(0, Math.min(action.payload, DEFAULT_STEPS.length - 1)),
      };

    case 'NEXT_STEP':
      return {
        ...state,
        currentStep: Math.min(state.currentStep + 1, DEFAULT_STEPS.length - 1),
      };

    case 'PREVIOUS_STEP':
      return {
        ...state,
        currentStep: Math.max(state.currentStep - 1, 0),
      };

    case 'SKIP_STEP':
      return {
        ...state,
        skippedSteps: [...state.skippedSteps, action.payload],
        currentStep: Math.min(state.currentStep + 1, DEFAULT_STEPS.length - 1),
      };

    case 'COMPLETE_ONBOARDING':
      return {
        ...state,
        isCompleted: true,
        completedAt: new Date(),
      };

    case 'RESET_ONBOARDING':
      return {
        ...initialState,
        userPreferences: state.userPreferences, // Preserve user preferences
      };

    case 'UPDATE_USER_PREFERENCES':
      return {
        ...state,
        userPreferences: {
          ...state.userPreferences,
          ...action.payload,
        },
      };

    case 'UPDATE_USER_PROFILE':
      return {
        ...state,
        userProfile: action.payload,
      };

    case 'LOAD_STATE':
      return action.payload;

    default:
      return state;
  }
}

// Create context
const OnboardingContext = createContext<OnboardingContextType | undefined>(undefined);

// Provider component
interface OnboardingProviderProps {
  children: ReactNode;
}

export function OnboardingProvider({ children }: OnboardingProviderProps) {
  const [state, dispatch] = useReducer(onboardingReducer, initialState);

  // Load state from localStorage on mount
  useEffect(() => {
    try {
      const savedState = localStorage.getItem(STORAGE_KEY);
      if (savedState) {
        const parsedState = JSON.parse(savedState);
        // Convert completedAt back to Date if it exists
        if (parsedState.completedAt) {
          parsedState.completedAt = new Date(parsedState.completedAt);
        }
        dispatch({ type: 'LOAD_STATE', payload: parsedState });
      }
    } catch (error) {
      console.warn('Failed to load onboarding state from localStorage:', error);
    }
  }, []);

  // Save state to localStorage whenever it changes
  useEffect(() => {
    try {
      localStorage.setItem(STORAGE_KEY, JSON.stringify(state));
    } catch (error) {
      console.warn('Failed to save onboarding state to localStorage:', error);
    }
  }, [state]);

  // Helper functions
  const nextStep = () => {
    if (state.currentStep < DEFAULT_STEPS.length - 1) {
      dispatch({ type: 'NEXT_STEP' });
    } else {
      // If we're at the last step, complete onboarding
      dispatch({ type: 'COMPLETE_ONBOARDING' });
    }
  };

  const previousStep = () => {
    dispatch({ type: 'PREVIOUS_STEP' });
  };

  const skipStep = () => {
    const currentStepId = DEFAULT_STEPS[state.currentStep]?.id;
    if (currentStepId && DEFAULT_STEPS[state.currentStep].canSkip) {
      dispatch({ type: 'SKIP_STEP', payload: currentStepId });
    }
  };

  const completeOnboarding = () => {
    dispatch({ type: 'COMPLETE_ONBOARDING' });
  };

  const resetOnboarding = () => {
    dispatch({ type: 'RESET_ONBOARDING' });
  };

  const updateUserPreferences = (preferences: Partial<OnboardingState['userPreferences']>) => {
    dispatch({ type: 'UPDATE_USER_PREFERENCES', payload: preferences });
  };

  const updateUserProfile = (profile: UserProfileFormData) => {
    dispatch({ type: 'UPDATE_USER_PROFILE', payload: profile });
  };

  const getCurrentStep = (): OnboardingStep | undefined => {
    return DEFAULT_STEPS[state.currentStep];
  };

  const canGoNext = (): boolean => {
    return state.currentStep < DEFAULT_STEPS.length - 1;
  };

  const canGoPrevious = (): boolean => {
    return state.currentStep > 0;
  };

  const contextValue: OnboardingContextType = {
    state,
    steps: DEFAULT_STEPS,
    dispatch,
    nextStep,
    previousStep,
    skipStep,
    completeOnboarding,
    resetOnboarding,
    updateUserPreferences,
    updateUserProfile,
    getCurrentStep,
    canGoNext,
    canGoPrevious,
  };

  return (
    <OnboardingContext.Provider value={contextValue}>
      {children}
    </OnboardingContext.Provider>
  );
}

// Custom hook to use onboarding context
export function useOnboardingContext(): OnboardingContextType {
  const context = useContext(OnboardingContext);
  if (context === undefined) {
    throw new Error('useOnboardingContext must be used within an OnboardingProvider');
  }
  return context;
}