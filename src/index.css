@tailwind base;
@tailwind components;
@tailwind utilities;

/* Definition of the design system. All colors, gradients, fonts, etc should be defined here. 
All colors MUST be HSL.
*/

@layer base {
  :root {
    /* ZARA Minimalism + Liquid Glass Design System */
    /* Pure whites and deep charcoals for Zara aesthetic */
    --background: 0 0% 100%;
    --foreground: 0 0% 12%;

    --card: 0 0% 100%;
    --card-foreground: 0 0% 12%;

    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 12%;

    --primary: 0 0% 0%;
    --primary-foreground: 0 0% 100%;

    --secondary: 0 0% 97%;
    --secondary-foreground: 0 0% 12%;

    --muted: 0 0% 96%;
    --muted-foreground: 0 0% 54%;

    --accent: 0 0% 95%;
    --accent-foreground: 0 0% 12%;

    --destructive: 0 84% 60%;
    --destructive-foreground: 0 0% 100%;

    --border: 0 0% 90%;
    --input: 0 0% 90%;
    --ring: 0 0% 0%;

    /* Enhanced Zara Color Palette */
    --zara-white: 0 0% 100%;
    --zara-black: 0 0% 0%;
    --zara-charcoal: 0 0% 12%;
    --zara-light-gray: 0 0% 97%;
    --zara-medium-gray: 0 0% 90%;
    --zara-dark-gray: 0 0% 54%;

    /* Advanced Liquid Glass System */
    --glass-bg: 255 255 255;
    --glass-border: 255 255 255;
    --glass-shadow: 0 0% 0%;
    
    /* Multiple glass opacity levels */
    --glass-subtle: 0.1;
    --glass-light: 0.15;
    --glass-medium: 0.2;
    --glass-strong: 0.4;
    --glass-prominent: 0.6;
    --glass-navigation: 0.7;
    --glass-modal: 0.8;
    
    /* Multiple blur intensities */
    --blur-xs: 4px;
    --blur-sm: 8px;
    --blur-md: 16px;
    --blur-lg: 24px;
    --blur-xl: 32px;
    --blur-2xl: 40px;
    
    /* Glass reflection and refraction */
    --glass-reflection: rgba(255, 255, 255, 0.3);
    --glass-refraction: rgba(255, 255, 255, 0.1);
    --glass-highlight: rgba(255, 255, 255, 0.5);
    
    /* Premium animation timing */
    --timing-instant: 100ms;
    --timing-fast: 150ms;
    --timing-normal: 300ms;
    --timing-slow: 500ms;
    --timing-extra-slow: 800ms;
    --timing-luxurious: 1200ms;
    
    /* Premium easing curves */
    --ease-glass: cubic-bezier(0.4, 0, 0.2, 1);
    --ease-bounce: cubic-bezier(0.68, -0.55, 0.265, 1.55);
    --ease-smooth: cubic-bezier(0.25, 0.46, 0.45, 0.94);
    --ease-premium: cubic-bezier(0.23, 1, 0.32, 1);
    --ease-liquid: cubic-bezier(0.16, 1, 0.3, 1);
    
    /* Zara-inspired spacing system */
    --space-xs: 8px;
    --space-sm: 16px;
    --space-md: 24px;
    --space-lg: 32px;
    --space-xl: 48px;
    --space-2xl: 64px;
    --space-3xl: 96px;

    --radius: 1rem;

    --sidebar-background: 0 0% 100%;
    --sidebar-foreground: 0 0% 12%;
    --sidebar-primary: 0 0% 0%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 0 0% 97%;
    --sidebar-accent-foreground: 0 0% 12%;
    --sidebar-border: 0 0% 90%;
    --sidebar-ring: 0 0% 0%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground font-sans;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  }
}

@layer components {
  /* Enhanced Zara Typography System */
  .zara-hero {
    font-size: 2rem;
    font-weight: 100;
    letter-spacing: 0.05em;
    line-height: 1.1;
    color: hsl(var(--zara-charcoal));
  }

  .zara-title {
    font-size: 1.5rem;
    font-weight: 200;
    letter-spacing: 0.03em;
    line-height: 1.2;
    color: hsl(var(--zara-charcoal));
  }

  .zara-subtitle {
    font-size: 1.125rem;
    font-weight: 300;
    letter-spacing: 0.02em;
    line-height: 1.3;
    color: hsl(var(--zara-dark-gray));
  }

  .zara-body {
    font-size: 0.875rem;
    font-weight: 400;
    letter-spacing: 0.01em;
    line-height: 1.5;
    color: hsl(var(--zara-charcoal));
  }

  .zara-caption {
    font-size: 0.75rem;
    font-weight: 300;
    letter-spacing: 0.02em;
    line-height: 1.4;
    color: hsl(var(--zara-dark-gray));
    text-transform: uppercase;
  }

  /* Advanced Liquid Glass Effect Utilities */
  .glass-subtle {
    backdrop-filter: blur(var(--blur-sm));
    background: rgba(var(--glass-bg), var(--glass-subtle));
    border: 1px solid rgba(var(--glass-border), var(--glass-light));
    box-shadow: 
      0 4px 16px rgba(0, 0, 0, 0.04),
      inset 0 1px 0 var(--glass-highlight);
  }

  .glass-light {
    backdrop-filter: blur(var(--blur-md));
    background: rgba(var(--glass-bg), var(--glass-light));
    border: 1px solid rgba(var(--glass-border), var(--glass-medium));
    box-shadow: 
      0 6px 20px rgba(0, 0, 0, 0.06),
      inset 0 1px 0 var(--glass-highlight);
  }

  .glass-medium {
    backdrop-filter: blur(var(--blur-md));
    background: rgba(var(--glass-bg), var(--glass-medium));
    border: 1px solid rgba(var(--glass-border), var(--glass-strong));
    box-shadow: 
      0 8px 32px rgba(0, 0, 0, 0.08),
      inset 0 1px 0 var(--glass-highlight),
      inset 0 -1px 0 var(--glass-refraction);
  }

  .glass-strong {
    backdrop-filter: blur(var(--blur-lg));
    background: rgba(var(--glass-bg), var(--glass-strong));
    border: 1px solid rgba(var(--glass-border), var(--glass-prominent));
    box-shadow: 
      0 12px 40px rgba(0, 0, 0, 0.12),
      inset 0 2px 0 var(--glass-highlight),
      inset 0 -1px 0 var(--glass-refraction);
  }

  .glass-prominent {
    backdrop-filter: blur(var(--blur-xl));
    background: rgba(var(--glass-bg), var(--glass-prominent));
    border: 1px solid rgba(var(--glass-border), var(--glass-navigation));
    box-shadow: 
      0 16px 48px rgba(0, 0, 0, 0.15),
      inset 0 2px 0 var(--glass-highlight),
      inset 0 -2px 0 var(--glass-refraction);
  }

  .glass-navigation {
    backdrop-filter: blur(var(--blur-xl));
    background: rgba(var(--glass-bg), var(--glass-navigation));
    border-top: 1px solid rgba(var(--glass-border), var(--glass-prominent));
    box-shadow: 
      0 -8px 32px rgba(0, 0, 0, 0.08),
      inset 0 1px 0 var(--glass-highlight);
  }

  .glass-modal {
    backdrop-filter: blur(var(--blur-2xl));
    background: rgba(var(--glass-bg), var(--glass-modal));
    border: 1px solid rgba(var(--glass-border), var(--glass-prominent));
    box-shadow: 
      0 24px 64px rgba(0, 0, 0, 0.20),
      inset 0 2px 0 var(--glass-highlight),
      inset 0 -2px 0 var(--glass-refraction);
  }

  .glass-hero {
    backdrop-filter: blur(var(--blur-lg));
    background: rgba(var(--glass-bg), var(--glass-medium));
    border: 1px solid rgba(var(--glass-border), var(--glass-strong));
    box-shadow: 
      0 20px 56px rgba(0, 0, 0, 0.10),
      inset 0 2px 0 var(--glass-highlight),
      inset 0 -1px 0 var(--glass-refraction);
  }

  .glass-product {
    backdrop-filter: blur(var(--blur-sm));
    background: rgba(var(--glass-bg), var(--glass-subtle));
    border: 1px solid rgba(var(--glass-border), var(--glass-light));
    box-shadow: 
      0 2px 8px rgba(0, 0, 0, 0.03),
      inset 0 1px 0 var(--glass-highlight);
    transition: all var(--timing-normal) var(--ease-glass);
  }

  .glass-product:hover {
    backdrop-filter: blur(var(--blur-md));
    background: rgba(var(--glass-bg), var(--glass-light));
    box-shadow: 
      0 8px 24px rgba(0, 0, 0, 0.08),
      inset 0 1px 0 var(--glass-highlight);
    transform: translateY(-2px);
  }

  /* Morphing Glass Effects */
  .glass-morphing {
    transition: all var(--timing-slow) var(--ease-liquid);
  }

  .glass-morphing:hover {
    backdrop-filter: blur(var(--blur-xl));
    background: rgba(var(--glass-bg), var(--glass-strong));
    transform: scale(1.02);
    box-shadow: 
      0 16px 48px rgba(0, 0, 0, 0.15),
      inset 0 2px 0 var(--glass-highlight),
      inset 0 -2px 0 var(--glass-refraction);
  }

  /* Premium Animation Classes */
  .animate-glass-fade-in {
    animation: glass-fade-in var(--timing-slow) var(--ease-glass);
  }

  .animate-glass-scale-in {
    animation: glass-scale-in var(--timing-normal) var(--ease-premium);
  }

  .animate-glass-slide-up {
    animation: glass-slide-up var(--timing-slow) var(--ease-liquid);
  }

  .animate-liquid-morph {
    animation: liquid-morph var(--timing-luxurious) var(--ease-liquid);
  }

  /* Smooth Transition Utilities */
  .transition-glass {
    transition: all var(--timing-normal) var(--ease-glass);
  }

  .transition-premium {
    transition: all var(--timing-slow) var(--ease-premium);
  }

  .transition-liquid {
    transition: all var(--timing-luxurious) var(--ease-liquid);
  }

  .transition-instant {
    transition: all var(--timing-instant) var(--ease-glass);
  }

  /* Enhanced Interactive States */
  .glass-interactive {
    transition: all var(--timing-normal) var(--ease-glass);
  }

  .glass-interactive:hover {
    transform: translateY(-1px) scale(1.01);
  }

  .glass-interactive:active {
    transform: translateY(0) scale(0.99);
    transition: all var(--timing-fast) var(--ease-glass);
  }

  /* Fashion-forward image styling */
  .fashion-image {
    object-fit: cover;
    transition: all var(--timing-slow) var(--ease-premium);
    filter: contrast(1.05) saturate(0.95);
  }
  
  .fashion-image:hover {
    transform: scale(1.03);
    filter: contrast(1.1) saturate(1);
  }

  /* Enhanced Navigation States */
  .tab-bar-hidden {
    transform: translateY(100%);
    opacity: 0;
    transition: all var(--timing-normal) var(--ease-glass);
  }
  
  .tab-bar-visible {
    transform: translateY(0);
    opacity: 1;
    transition: all var(--timing-normal) var(--ease-glass);
  }

  /* Enhanced Form Styling */
  .glass-input {
    backdrop-filter: blur(var(--blur-sm));
    background: rgba(var(--glass-bg), var(--glass-subtle));
    border: 1px solid rgba(var(--glass-border), var(--glass-light));
    transition: all var(--timing-normal) var(--ease-glass);
    box-shadow: 
      0 2px 8px rgba(0, 0, 0, 0.03),
      inset 0 1px 0 var(--glass-highlight);
  }

  .glass-input:focus {
    outline: none;
    backdrop-filter: blur(var(--blur-md));
    background: rgba(var(--glass-bg), var(--glass-light));
    border-color: rgba(var(--glass-border), var(--glass-medium));
    box-shadow:
      0 8px 24px rgba(0, 0, 0, 0.08),
      inset 0 1px 0 var(--glass-highlight),
      0 0 0 2px rgba(0, 0, 0, 0.1);
  }
}

/* Premium Keyframe Animations */
@keyframes glass-fade-in {
  from {
    opacity: 0;
    backdrop-filter: blur(0);
  }
  to {
    opacity: 1;
    backdrop-filter: blur(var(--blur-md));
  }
}

@keyframes glass-scale-in {
  from {
    opacity: 0;
    transform: scale(0.95);
    backdrop-filter: blur(0);
  }
  to {
    opacity: 1;
    transform: scale(1);
    backdrop-filter: blur(var(--blur-md));
  }
}

@keyframes glass-slide-up {
  from {
    opacity: 0;
    transform: translateY(20px);
    backdrop-filter: blur(0);
  }
  to {
    opacity: 1;
    transform: translateY(0);
    backdrop-filter: blur(var(--blur-md));
  }
}

@keyframes liquid-morph {
  0% {
    backdrop-filter: blur(var(--blur-sm));
    background: rgba(var(--glass-bg), var(--glass-subtle));
  }
  50% {
    backdrop-filter: blur(var(--blur-xl));
    background: rgba(var(--glass-bg), var(--glass-strong));
    transform: scale(1.05);
  }
  100% {
    backdrop-filter: blur(var(--blur-md));
    background: rgba(var(--glass-bg), var(--glass-medium));
    transform: scale(1);
  }
}

@keyframes fadeInUp {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}